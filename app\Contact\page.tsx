"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import {
  ArrowLeft,
  Mail,
  User,
  MessageSquare,
  Phone,
  Send,
  CheckCircle,
  AlertCircle,
  Loader2,
  Clock,
  MessageCircle
} from "lucide-react"
import { cn } from "@/lib/utils"
import {
  ContactFormData,
  ContactFormErrors,
  validateContactForm,
  getContactMetadata,
  CONTACT_SUBJECTS,
  getCharacterCount,
  checkRateLimit,
  setRateLimitTimestamp,
  sanitizeInput
} from "@/lib/contact"
import { createContactMessage } from "@/lib/firestore"

export default function ContactPage() {
  const [formData, setFormData] = useState<ContactFormData>({
    name: "",
    email: "",
    subject: "",
    message: "",
    phone: ""
  })
  const [errors, setErrors] = useState<ContactFormErrors>({})
  const [loading, setLoading] = useState(false)
  const [submitted, setSubmitted] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target

    // Sanitize input
    const sanitizedValue = sanitizeInput(value)

    setFormData(prev => ({
      ...prev,
      [name]: sanitizedValue
    }))

    // Clear field error when user starts typing
    if (errors[name as keyof ContactFormErrors]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined
      }))
    }

    // Clear general error
    if (error) {
      setError(null)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Check rate limiting
    const rateLimitCheck = checkRateLimit()
    if (!rateLimitCheck.allowed) {
      setError(`Please wait ${rateLimitCheck.timeUntilReset} minutes before submitting another message.`)
      return
    }

    // Validate form
    const validationErrors = validateContactForm(formData)
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors)
      return
    }

    setLoading(true)
    setError(null)

    try {
      // Get metadata
      const metadata = getContactMetadata()

      // Create contact message
      await createContactMessage({
        name: formData.name,
        email: formData.email,
        subject: formData.subject,
        message: formData.message,
        phone: formData.phone || undefined,
        userAgent: metadata.userAgent,
        ipAddress: metadata.ipAddress
      })

      // Set rate limit timestamp
      setRateLimitTimestamp()

      // Show success state
      setSubmitted(true)

      // Reset form
      setFormData({
        name: "",
        email: "",
        subject: "",
        message: "",
        phone: ""
      })
      setErrors({})

    } catch (err) {
      console.error('Contact form submission error:', err)
      setError('Failed to send message. Please try again later.')
    } finally {
      setLoading(false)
    }
  }

  const messageCharCount = getCharacterCount(formData.message)

  if (submitted) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white border-b border-gray-200 sticky top-0 z-40">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center space-x-4">
                <Link href="/">
                  <Button variant="ghost" size="sm" className="hover:bg-gray-100">
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back to Home
                  </Button>
                </Link>
                <div className="h-6 w-px bg-gray-300" />
                <div>
                  <h1 className="text-lg font-semibold text-gray-900">Contact Us</h1>
                  <p className="text-sm text-gray-500">Get in touch with our team</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Badge variant="outline" className="text-xs">
                  <MessageCircle className="w-3 h-3 mr-1" />
                  Contact Form
                </Badge>
              </div>
            </div>
          </div>
        </header>

        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <Card className="border-0 shadow-lg">
            <CardContent className="p-12 text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-6">
                <CheckCircle className="w-8 h-8 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Message Sent Successfully!</h2>
              <p className="text-gray-600 mb-8 max-w-md mx-auto">
                Thank you for contacting us. We've received your message and will get back to you within 24 hours.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/">
                  <Button variant="outline">
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back to Home
                  </Button>
                </Link>
                <Button onClick={() => setSubmitted(false)}>
                  <MessageSquare className="w-4 h-4 mr-2" />
                  Send Another Message
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link href="/">
                <Button variant="ghost" size="sm" className="hover:bg-gray-100">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Home
                </Button>
              </Link>
              <div className="h-6 w-px bg-gray-300" />
              <div>
                <h1 className="text-lg font-semibold text-gray-900">Contact Us</h1>
                <p className="text-sm text-gray-500">Get in touch with our team</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className="text-xs">
                <MessageCircle className="w-3 h-3 mr-1" />
                Contact Form
              </Badge>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Contact Information Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-24 space-y-6">
              {/* Contact Info Card */}
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    <MessageCircle className="w-5 h-5 mr-2 text-indigo-600" />
                    Get in Touch
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-start space-x-3">
                      <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center flex-shrink-0">
                        <Mail className="w-4 h-4 text-white" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900">Email Support</h4>
                        <p className="text-sm text-gray-600"><EMAIL></p>
                        <p className="text-xs text-gray-500 mt-1">Response within 24 hours</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center flex-shrink-0">
                        <Clock className="w-4 h-4 text-white" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900">Business Hours</h4>
                        <p className="text-sm text-gray-600">Monday - Friday</p>
                        <p className="text-xs text-gray-500 mt-1">9:00 AM - 6:00 PM EST</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center flex-shrink-0">
                        <MessageSquare className="w-4 h-4 text-white" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900">Live Chat</h4>
                        <p className="text-sm text-gray-600">Available for Pro+ users</p>
                        <p className="text-xs text-gray-500 mt-1">Instant support during business hours</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Contact Form */}
          <div className="lg:col-span-2">
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center">
                    <MessageCircle className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-2xl">Send us a Message</CardTitle>
                    <p className="text-gray-600 mt-1">We'd love to hear from you</p>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {/* Error Message */}
                {error && (
                  <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl">
                    <div className="flex items-start space-x-3">
                      <AlertCircle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="font-semibold text-red-900 mb-1">Error</h4>
                        <p className="text-red-800 text-sm">{error}</p>
                      </div>
                    </div>
                  </div>
                )}

                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Name and Email Row */}
                  <div className="grid md:grid-cols-2 gap-6">
                    {/* Name Field */}
                    <div className="space-y-2">
                      <label htmlFor="name" className="text-sm font-semibold text-gray-700 flex items-center space-x-2">
                        <User className="w-4 h-4" />
                        <span>Full Name</span>
                        <span className="text-red-500">*</span>
                      </label>
                      <Input
                        id="name"
                        name="name"
                        type="text"
                        placeholder="Enter your full name"
                        value={formData.name}
                        onChange={handleInputChange}
                        className={cn(
                          "h-12 text-base border-2 focus:ring-4 transition-all duration-300 bg-white/50",
                          errors.name
                            ? "border-red-300 focus:border-red-500 focus:ring-red-500/20"
                            : "border-gray-200 focus:border-indigo-500 focus:ring-indigo-500/20"
                        )}
                        required
                        disabled={loading}
                        aria-describedby={errors.name ? "name-error" : undefined}
                      />
                      {errors.name && (
                        <p id="name-error" className="text-sm text-red-600 flex items-center space-x-1">
                          <AlertCircle className="w-4 h-4" />
                          <span>{errors.name}</span>
                        </p>
                      )}
                    </div>

                    {/* Email Field */}
                    <div className="space-y-2">
                      <label htmlFor="email" className="text-sm font-semibold text-gray-700 flex items-center space-x-2">
                        <Mail className="w-4 h-4" />
                        <span>Email Address</span>
                        <span className="text-red-500">*</span>
                      </label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        placeholder="Enter your email address"
                        value={formData.email}
                        onChange={handleInputChange}
                        className={cn(
                          "h-12 text-base border-2 focus:ring-4 transition-all duration-300 bg-white/50",
                          errors.email
                            ? "border-red-300 focus:border-red-500 focus:ring-red-500/20"
                            : "border-gray-200 focus:border-indigo-500 focus:ring-indigo-500/20"
                        )}
                        required
                        disabled={loading}
                        aria-describedby={errors.email ? "email-error" : undefined}
                      />
                      {errors.email && (
                        <p id="email-error" className="text-sm text-red-600 flex items-center space-x-1">
                          <AlertCircle className="w-4 h-4" />
                          <span>{errors.email}</span>
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Subject and Phone Row */}
                  <div className="grid md:grid-cols-2 gap-6">
                    {/* Subject Field */}
                    <div className="space-y-2">
                      <label htmlFor="subject" className="text-sm font-semibold text-gray-700 flex items-center space-x-2">
                        <MessageSquare className="w-4 h-4" />
                        <span>Subject</span>
                        <span className="text-red-500">*</span>
                      </label>
                      <select
                        id="subject"
                        name="subject"
                        value={formData.subject}
                        onChange={handleInputChange}
                        className={cn(
                          "h-12 w-full text-base border-2 focus:ring-4 transition-all duration-300 bg-white/50 rounded-xl px-4",
                          errors.subject
                            ? "border-red-300 focus:border-red-500 focus:ring-red-500/20"
                            : "border-gray-200 focus:border-indigo-500 focus:ring-indigo-500/20"
                        )}
                        required
                        disabled={loading}
                        aria-describedby={errors.subject ? "subject-error" : undefined}
                      >
                        <option value="">Select a subject</option>
                        {CONTACT_SUBJECTS.map((subject) => (
                          <option key={subject} value={subject}>
                            {subject}
                          </option>
                        ))}
                      </select>
                      {errors.subject && (
                        <p id="subject-error" className="text-sm text-red-600 flex items-center space-x-1">
                          <AlertCircle className="w-4 h-4" />
                          <span>{errors.subject}</span>
                        </p>
                      )}
                    </div>

                    {/* Phone Field */}
                    <div className="space-y-2">
                      <label htmlFor="phone" className="text-sm font-semibold text-gray-700 flex items-center space-x-2">
                        <Phone className="w-4 h-4" />
                        <span>Phone Number</span>
                        <span className="text-gray-400 text-xs">(Optional)</span>
                      </label>
                      <Input
                        id="phone"
                        name="phone"
                        type="tel"
                        placeholder="Enter your phone number"
                        value={formData.phone}
                        onChange={handleInputChange}
                        className={cn(
                          "h-12 text-base border-2 focus:ring-4 transition-all duration-300 bg-white/50",
                          errors.phone
                            ? "border-red-300 focus:border-red-500 focus:ring-red-500/20"
                            : "border-gray-200 focus:border-indigo-500 focus:ring-indigo-500/20"
                        )}
                        disabled={loading}
                        aria-describedby={errors.phone ? "phone-error" : undefined}
                      />
                      {errors.phone && (
                        <p id="phone-error" className="text-sm text-red-600 flex items-center space-x-1">
                          <AlertCircle className="w-4 h-4" />
                          <span>{errors.phone}</span>
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Message Field */}
                  <div className="space-y-2">
                    <label htmlFor="message" className="text-sm font-semibold text-gray-700 flex items-center space-x-2">
                      <MessageSquare className="w-4 h-4" />
                      <span>Message</span>
                      <span className="text-red-500">*</span>
                    </label>
                    <Textarea
                      id="message"
                      name="message"
                      placeholder="Tell us how we can help you..."
                      value={formData.message}
                      onChange={handleInputChange}
                      rows={6}
                      className={cn(
                        "text-base border-2 focus:ring-4 transition-all duration-300 bg-white/50 resize-none",
                        errors.message
                          ? "border-red-300 focus:border-red-500 focus:ring-red-500/20"
                          : "border-gray-200 focus:border-indigo-500 focus:ring-indigo-500/20"
                      )}
                      required
                      disabled={loading}
                      aria-describedby={errors.message ? "message-error" : "message-help"}
                    />
                    <div className="flex items-center justify-between">
                      <div>
                        {errors.message && (
                          <p id="message-error" className="text-sm text-red-600 flex items-center space-x-1">
                            <AlertCircle className="w-4 h-4" />
                            <span>{errors.message}</span>
                          </p>
                        )}
                        {!errors.message && (
                          <p id="message-help" className="text-xs text-gray-500">
                            Please provide as much detail as possible to help us assist you better.
                          </p>
                        )}
                      </div>
                      <div className="text-xs text-gray-500">
                        <span className={cn(
                          messageCharCount.count < 10 ? "text-red-500" :
                          messageCharCount.count > 1800 ? "text-orange-500" : "text-gray-500"
                        )}>
                          {messageCharCount.count}
                        </span>
                        <span className="text-gray-400">/2000</span>
                      </div>
                    </div>
                  </div>

                  {/* Submit Button */}
                  <div className="pt-4">
                    <Button
                      type="submit"
                      className="w-full h-12 text-base font-semibold bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
                      disabled={loading || Object.keys(errors).length > 0}
                    >
                      {loading ? (
                        <>
                          <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                          Sending Message...
                        </>
                      ) : (
                        <>
                          <Send className="w-5 h-5 mr-2" />
                          Send Message
                        </>
                      )}
                    </Button>
                  </div>

                  {/* Privacy Notice */}
                  <div className="pt-4 border-t border-gray-200">
                    <p className="text-xs text-gray-500 text-center">
                      By submitting this form, you agree to our{" "}
                      <Link href="/Privacy" className="text-indigo-600 hover:text-indigo-800 underline">
                        Privacy Policy
                      </Link>{" "}
                      and{" "}
                      <Link href="/Terms" className="text-indigo-600 hover:text-indigo-800 underline">
                        Terms of Service
                      </Link>
                      . We'll respond within 24 hours.
                    </p>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}