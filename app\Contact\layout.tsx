import type { Metadata } from "next"

export const metadata: Metadata = {
  title: "Contact Us - Promptly",
  description: "Get in touch with the Promptly team. We're here to help with any questions about our AI platform, technical support, billing, or partnership opportunities.",
  keywords: ["contact", "support", "help", "AI platform", "customer service", "technical support"],
  openGraph: {
    title: "Contact Us - Promptly",
    description: "Get in touch with the Promptly team. We're here to help with any questions about our AI platform.",
    type: "website",
  },
  twitter: {
    card: "summary",
    title: "Contact Us - Promptly",
    description: "Get in touch with the Promptly team. We're here to help with any questions about our AI platform.",
  },
}

export default function ContactLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
