"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  ArrowLeft,
  Shield,
  AlertTriangle,
  Clock,
  CreditCard,
  Key,
  Ban,
  FileText,
  Scale,
  Mail,
  ChevronRight
} from "lucide-react"
import Link from "next/link"
import { cn } from "@/lib/utils"

export default function TermsAndConditionsPage() {
  const [activeSection, setActiveSection] = useState("")

  // Table of contents data
  const sections = [
    { id: "account-usage", title: "Account Usage & Restrictions", icon: Shield },
    { id: "subscription", title: "Subscription & Pricing", icon: CreditCard },
    { id: "usage-limits", title: "Usage Limits & Fair Use", icon: Clock },
    { id: "prohibited-conduct", title: "Prohibited Conduct", icon: Ban },
    { id: "termination", title: "Termination", icon: <PERSON><PERSON><PERSON>rian<PERSON> },
    { id: "modifications", title: "Modifications", icon: FileText },
    { id: "contact", title: "Contact Information", icon: Mail }
  ]

  // Scroll spy effect
  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY + 100

      for (const section of sections) {
        const element = document.getElementById(section.id)
        if (element) {
          const offsetTop = element.offsetTop
          const offsetBottom = offsetTop + element.offsetHeight

          if (scrollPosition >= offsetTop && scrollPosition < offsetBottom) {
            setActiveSection(section.id)
            break
          }
        }
      }
    }

    window.addEventListener("scroll", handleScroll)
    handleScroll() // Initial call

    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: "smooth", block: "start" })
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link href="/">
                <Button variant="ghost" size="sm" className="hover:bg-gray-100">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Home
                </Button>
              </Link>
              <div className="h-6 w-px bg-gray-300" />
              <div>
                <h1 className="text-lg font-semibold text-gray-900">Terms and Conditions</h1>
                <p className="text-sm text-gray-500">Effective Date: May 28, 2025</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className="text-xs">
                <FileText className="w-3 h-3 mr-1" />
                Legal Document
              </Badge>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid lg:grid-cols-4 gap-8">
          {/* Table of Contents - Sticky Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-24">
              <Card className="border-0 shadow-lg">
                <CardHeader className="pb-4">
                  <CardTitle className="text-lg flex items-center">
                    <FileText className="w-5 h-5 mr-2 text-indigo-600" />
                    Contents
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <nav className="space-y-1">
                    {sections.map((section) => (
                      <button
                        key={section.id}
                        onClick={() => scrollToSection(section.id)}
                        className={cn(
                          "w-full text-left px-3 py-2 rounded-lg text-sm transition-all duration-200 flex items-center group",
                          activeSection === section.id
                            ? "bg-gradient-to-r from-indigo-50 to-purple-50 text-indigo-700 border-l-2 border-indigo-500"
                            : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                        )}
                      >
                        <section.icon className={cn(
                          "w-4 h-4 mr-2 flex-shrink-0",
                          activeSection === section.id ? "text-indigo-600" : "text-gray-400"
                        )} />
                        <span className="flex-1">{section.title}</span>
                        <ChevronRight className={cn(
                          "w-3 h-3 transition-transform",
                          activeSection === section.id ? "rotate-90 text-indigo-600" : "text-gray-400 group-hover:translate-x-1"
                        )} />
                      </button>
                    ))}
                  </nav>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            <div className="space-y-8">
              {/* Introduction */}
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center">
                      <Scale className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <CardTitle className="text-2xl">Terms and Conditions</CardTitle>
                      <p className="text-gray-600 mt-1">Welcome to Promptly - Your AI Platform</p>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="prose prose-gray max-w-none">
                    <p className="text-lg text-gray-700 leading-relaxed">
                      Welcome to Promptly! These Terms and Conditions ("Terms") govern your use of our AI platform
                      and services. By accessing or using Promptly, you agree to be bound by these Terms.
                    </p>
                    <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-xl">
                      <div className="flex items-start space-x-3">
                        <AlertTriangle className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                        <div>
                          <h4 className="font-semibold text-blue-900 mb-1">Important Notice</h4>
                          <p className="text-blue-800 text-sm">
                            Please read these terms carefully. They contain important information about your rights
                            and obligations when using our service.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Account Usage and Restrictions */}
              <section id="account-usage">
                <Card className="border-0 shadow-lg">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-red-500 to-orange-500 rounded-lg flex items-center justify-center">
                        <Shield className="w-5 h-5 text-white" />
                      </div>
                      <CardTitle className="text-xl">Account Usage and Restrictions</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {/* Account Sharing Warning */}
                      <div className="p-4 bg-red-50 border border-red-200 rounded-xl">
                        <div className="flex items-start space-x-3">
                          <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
                          <div>
                            <h4 className="font-semibold text-red-900 mb-2">⚠️ CRITICAL: Account Sharing Prohibition</h4>
                            <p className="text-red-800 text-sm mb-2">
                              <strong>Account sharing is strictly prohibited and will result in immediate permanent ban.</strong>
                            </p>
                            <ul className="text-red-800 text-sm space-y-1 list-disc list-inside">
                              <li>Each account is for individual use only</li>
                              <li>Sharing login credentials is forbidden</li>
                              <li>Multiple users on one account will trigger automatic suspension</li>
                              <li>Violations result in permanent account termination without refund</li>
                            </ul>
                          </div>
                        </div>
                      </div>

                      {/* Multiple Trial Restrictions */}
                      <div className="p-4 bg-orange-50 border border-orange-200 rounded-xl">
                        <div className="flex items-start space-x-3">
                          <Ban className="w-5 h-5 text-orange-600 mt-0.5 flex-shrink-0" />
                          <div>
                            <h4 className="font-semibold text-orange-900 mb-2">Multiple Trial Account Restrictions</h4>
                            <p className="text-orange-800 text-sm mb-2">
                              Users are limited to one trial account per person/household.
                            </p>
                            <ul className="text-orange-800 text-sm space-y-1 list-disc list-inside">
                              <li>Creating multiple trial accounts is prohibited</li>
                              <li>Using different email addresses to bypass trial limits is forbidden</li>
                              <li>We monitor for duplicate accounts using various detection methods</li>
                              <li>Violations may result in account suspension and loss of trial benefits</li>
                            </ul>
                          </div>
                        </div>
                      </div>

                      <div className="prose prose-gray max-w-none">
                        <h4 className="text-lg font-semibold text-gray-900 mb-3">General Account Requirements</h4>
                        <ul className="space-y-2 text-gray-700">
                          <li>You must be at least 13 years old to use our service</li>
                          <li>You must provide accurate and complete registration information</li>
                          <li>You are responsible for maintaining the security of your account</li>
                          <li>You must notify us immediately of any unauthorized use of your account</li>
                          <li>You may not use our service for any illegal or unauthorized purpose</li>
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </section>

              {/* Subscription and Pricing */}
              <section id="subscription">
                <Card className="border-0 shadow-lg">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center">
                        <CreditCard className="w-5 h-5 text-white" />
                      </div>
                      <CardTitle className="text-xl">Subscription and Pricing</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {/* No Refunds Policy */}
                      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-xl">
                        <div className="flex items-start space-x-3">
                          <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                          <div>
                            <h4 className="font-semibold text-yellow-900 mb-2">🚫 No Refunds Policy</h4>
                            <p className="text-yellow-800 text-sm mb-2">
                              <strong>All payments are final and non-refundable.</strong>
                            </p>
                            <ul className="text-yellow-800 text-sm space-y-1 list-disc list-inside">
                              <li>No refunds for unused credits or subscription time</li>
                              <li>No refunds for account suspensions due to policy violations</li>
                              <li>No refunds for service interruptions or technical issues</li>
                            </ul>
                          </div>
                        </div>
                      </div>

                      <div className="prose prose-gray max-w-none">
                        <h4 className="text-lg font-semibold text-gray-900 mb-3">Pricing and Billing</h4>
                        <ul className="space-y-2 text-gray-700">
                          <li>Subscription fees are billed in advance on a monthly or annual basis</li>
                          <li>Prices are subject to change with No Prior notice</li>
                        </ul>

                        <h4 className="text-lg font-semibold text-gray-900 mb-3 mt-6">Available Plans</h4>
                        <div className="grid md:grid-cols-3 gap-4 not-prose">
                          <div className="p-4 border border-gray-200 rounded-lg">
                            <h5 className="font-semibold text-gray-900">Starter</h5>
                            <p className="text-2xl font-bold text-gray-900">Free</p>
                            <p className="text-sm text-gray-600">100 credits/month</p>
                          </div>
                          <div className="p-4 border border-indigo-200 rounded-lg bg-indigo-50">
                            <h5 className="font-semibold text-indigo-900">Pro</h5>
                            <p className="text-2xl font-bold text-indigo-900">$19/month</p>
                            <p className="text-sm text-indigo-700">2,000 credits/month</p>
                          </div>
                          <div className="p-4 border border-gray-200 rounded-lg">
                            <h5 className="font-semibold text-gray-900">Business</h5>
                            <p className="text-2xl font-bold text-gray-900">$19/month</p>
                            <p className="text-sm text-gray-600">5,000 credits/month</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </section>

              {/* Usage Limits and Fair Use Policy */}
              <section id="usage-limits">
                <Card className="border-0 shadow-lg">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center">
                        <Clock className="w-5 h-5 text-white" />
                      </div>
                      <CardTitle className="text-xl">Usage Limits and Fair Use Policy</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {/* Rate Limits */}
                      <div className="p-4 bg-blue-50 border border-blue-200 rounded-xl">
                        <div className="flex items-start space-x-3">
                          <Clock className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                          <div>
                            <h4 className="font-semibold text-blue-900 mb-2">⏱️ Rate Limits and Quotas</h4>
                            <div className="text-blue-800 text-sm space-y-2">
                              <p><strong>Rate Limits:</strong></p>
                              <ul className="list-disc list-inside space-y-1 ml-4">
                                <li>Starter: 40 requests per hour</li>
                                <li>Pro: 80 requests per hour</li>
                                <li>Business: 120 requests per hour</li>
                              </ul>
                              <p><strong>Monthly Credit Limits:</strong></p>
                              <ul className="list-disc list-inside space-y-1 ml-4">
                                <li>Starter: 100 credits per month</li>
                                <li>Pro: 2,000 credits per month</li>
                                <li>Business: 5,000 credits per month</li>
                              </ul>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="prose prose-gray max-w-none">
                        <h4 className="text-lg font-semibold text-gray-900 mb-3">Fair Use Policy</h4>
                        <ul className="space-y-2 text-gray-700">
                          <li>Credits are consumed based on AI model usage and complexity</li>
                          <li>Excessive usage that impacts service performance may be throttled</li>
                          <li>Automated or bulk requests require prior approval</li>
                          <li>We reserve the right to implement additional limits to ensure fair access</li>
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </section>

              {/* Prohibited Conduct */}
              <section id="prohibited-conduct">
                <Card className="border-0 shadow-lg">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-red-500 to-pink-500 rounded-lg flex items-center justify-center">
                        <Ban className="w-5 h-5 text-white" />
                      </div>
                      <CardTitle className="text-xl">Prohibited Conduct</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="prose prose-gray max-w-none">
                      <p className="text-gray-700 mb-4">
                        The following activities are strictly prohibited when using our service:
                      </p>

                      <div className="grid md:grid-cols-2 gap-6 not-prose">
                        <div className="space-y-4">
                          <h4 className="text-lg font-semibold text-gray-900">Content Violations</h4>
                          <ul className="space-y-2 text-gray-700 text-sm">
                            <li>• Generating illegal, harmful, or offensive content</li>
                            <li>• Creating content that violates intellectual property rights</li>
                            <li>• Producing spam, malware, or phishing content</li>
                            <li>• Generating content that promotes violence or hatred</li>
                            <li>• Creating adult or sexually explicit content</li>
                          </ul>
                        </div>

                        <div className="space-y-4">
                          <h4 className="text-lg font-semibold text-gray-900">Technical Violations</h4>
                          <ul className="space-y-2 text-gray-700 text-sm">
                            <li>• Attempting to reverse engineer our services</li>
                            <li>• Circumventing usage limits or security measures</li>
                            <li>• Using automated tools without permission</li>
                            <li>• Interfering with service operation or other users</li>
                            <li>• Accessing unauthorized areas of our platform</li>
                          </ul>
                        </div>

                        <div className="space-y-4">
                          <h4 className="text-lg font-semibold text-gray-900">Commercial Violations</h4>
                          <ul className="space-y-2 text-gray-700 text-sm">
                            <li>• Reselling or redistributing our services</li>
                            <li>• Using our service to compete with us</li>
                            <li>• Commercial use without appropriate subscription</li>
                            <li>• Violating any applicable laws or regulations</li>
                          </ul>
                        </div>

                        <div className="space-y-4">
                          <h4 className="text-lg font-semibold text-gray-900">Account Violations</h4>
                          <ul className="space-y-2 text-gray-700 text-sm">
                            <li>• Sharing account credentials with others</li>
                            <li>• Creating multiple accounts to bypass limits</li>
                            <li>• Impersonating other individuals or entities</li>
                            <li>• Providing false or misleading information</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </section>
              {/* Termination */}
              <section id="termination">
                <Card className="border-0 shadow-lg">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
                        <AlertTriangle className="w-5 h-5 text-white" />
                      </div>
                      <CardTitle className="text-xl">Termination</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="prose prose-gray max-w-none">
                      <h4 className="text-lg font-semibold text-gray-900 mb-3">Termination Rights</h4>
                      <ul className="space-y-2 text-gray-700">
                        <li>You may terminate your account at any time through your account settings</li>
                        <li>We may terminate or suspend your account immediately for violations of these Terms</li>
                        <li>Upon termination, your access to the service will cease immediately</li>
                      </ul>

                      <h4 className="text-lg font-semibold text-gray-900 mb-3 mt-6">Effect of Termination</h4>
                      <ul className="space-y-2 text-gray-700">
                        <li>All rights and licenses granted to you will immediately cease</li>
                        <li>You must cease all use of our service and delete any downloaded content</li>
                        <li>We may delete your account data after a reasonable period</li>
                        <li>Provisions that should survive termination will remain in effect</li>
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              </section>

              {/* Modifications */}
              <section id="modifications">
                <Card className="border-0 shadow-lg">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-teal-500 to-green-500 rounded-lg flex items-center justify-center">
                        <FileText className="w-5 h-5 text-white" />
                      </div>
                      <CardTitle className="text-xl">Modifications</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="prose prose-gray max-w-none">
                      <h4 className="text-lg font-semibold text-gray-900 mb-3">Updates to Terms</h4>
                      <ul className="space-y-2 text-gray-700">
                        <li>We may update these Terms from time to time</li>
                        <li>We will notify you of material changes via email or service notification</li>
                        <li>Changes become effective 30 days after notification unless otherwise specified</li>
                        <li>Your continued use of the service constitutes acceptance of updated Terms</li>
                        <li>If you disagree with changes, you may terminate your account</li>
                      </ul>

                      <h4 className="text-lg font-semibold text-gray-900 mb-3 mt-6">Service Modifications</h4>
                      <ul className="space-y-2 text-gray-700">
                        <li>We may modify, suspend, or discontinue any part of our service</li>
                        <li>We will provide reasonable notice for significant service changes</li>
                        <li>We are not liable for any modification, suspension, or discontinuation</li>
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              </section>

              {/* Limitation of Liability */}
              <section id="liability">
                <Card className="border-0 shadow-lg">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-gray-500 to-slate-500 rounded-lg flex items-center justify-center">
                        <Scale className="w-5 h-5 text-white" />
                      </div>
                      <CardTitle className="text-xl">Limitation of Liability</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="prose prose-gray max-w-none">
                      <div className="p-4 bg-gray-50 border border-gray-200 rounded-xl mb-4">
                        <p className="text-gray-800 text-sm font-medium">
                          TO THE MAXIMUM EXTENT PERMITTED BY LAW, OUR LIABILITY IS LIMITED AS FOLLOWS:
                        </p>
                      </div>

                      <ul className="space-y-2 text-gray-700">
                        <li>We provide our service "as is" without warranties of any kind</li>
                        <li>We disclaim all warranties, express or implied, including merchantability and fitness</li>
                        <li>We are not liable for indirect, incidental, special, or consequential damages</li>
                        <li>Our total liability will not exceed the amount you paid in the last 12 months</li>
                        <li>We are not responsible for third-party content or services</li>
                        <li>Some jurisdictions may not allow these limitations</li>
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              </section>              

              {/* Contact Information */}
              <section id="contact">
                <Card className="border-0 shadow-lg">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg flex items-center justify-center">
                        <Mail className="w-5 h-5 text-white" />
                      </div>
                      <CardTitle className="text-xl">Contact Information</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="prose prose-gray max-w-none">
                      <p className="text-gray-700 mb-4">
                        If you have any questions about these Terms and Conditions, please contact us:
                      </p>

                      <div className="grid md:grid-cols-2 gap-6 not-prose">
                        <div className="space-y-3">
                          <h4 className="text-lg font-semibold text-gray-900">General Inquiries</h4>
                          <div className="space-y-2 text-gray-700">
                            <p className="flex items-center">
                              <Mail className="w-4 h-4 mr-2 text-gray-500" />
                              <EMAIL>
                            </p>
                            <p className="text-sm text-gray-600">
                              Response time: 24-48 hours
                            </p>
                          </div>
                        </div>

                        <div className="space-y-3">
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </section>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="flex items-center space-x-4">
              <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center">
                <Scale className="w-4 h-4 text-white" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900">Promptly</p>
                <p className="text-xs text-gray-500">All your AI, one smart platform</p>
              </div>
            </div>

            <div className="flex items-center space-x-6 text-sm text-gray-600">
              <Link href="/contact" className="hover:text-gray-900 transition-colors">
                Contact
              </Link>
            </div>

            <div className="text-xs text-gray-500">
              © 2025 Promptly. All rights reserved.
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}