"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  <PERSON><PERSON><PERSON>,
  Code,
  FileText,
  MessageSquare,
  Lightbulb,
  Search,
  ArrowRight
} from "lucide-react"
import { cn } from "@/lib/utils"

interface StarterQuestionsProps {
  onSendMessage?: (message: string) => void
}

const starterQuestions = [
  {
    id: 3,
    category: "Writing",
    icon: FileText,
    title: "Improve my writing",
    question: "Help me write, edit, or improve any type of content - emails, documents, or creative writing.",
    color: "from-green-500 to-emerald-500"
  },
  {
    id: 4,
    category: "Analysis",
    icon: Search,
    title: "Analyze and explain",
    question: "Help me analyze data, explain complex concepts, or break down difficult topics.",
    color: "from-orange-500 to-red-500"
  },
  {
    id: 5,
    category: "Learning",
    icon: Lightbulb,
    title: "Learn something new",
    question: "Teach me about a topic I'm curious about or help me understand something better.",
    color: "from-yellow-500 to-orange-500"
  },
]

export function StarterQuestions({ onSendMessage }: StarterQuestionsProps) {
  const handleQuestionClick = (question: string) => {
    if (onSendMessage) {
      onSendMessage(question)
    }
  }

  return (
    <div className="flex-1 flex items-center justify-center p-8">
      <div className="w-full max-w-4xl">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="w-20 h-20 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
            <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-3">
            How can I help you today?
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Choose a starting point below, or ask me anything! I'm here to assist with creative tasks,
            coding, writing, analysis, learning, and more.
          </p>
        </div>

        {/* Starter Questions Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {starterQuestions.map((item) => {
            const IconComponent = item.icon
            return (
              <Button
                key={item.id}
                variant="ghost"
                onClick={() => handleQuestionClick(item.question)}
                className={cn(
                  "h-auto min-h-[120px] p-6 text-left flex flex-col items-start justify-start",
                  "border border-gray-200 rounded-xl hover:border-gray-300",
                  "hover:shadow-md transition-all duration-200",
                  "bg-white hover:bg-gray-50",
                  "group cursor-pointer w-full overflow-hidden"
                )}
              >
                {/* Icon and Category */}
                <div className="flex items-center justify-between w-full flex-shrink-0 mb-3">
                  <div className="flex items-center space-x-3 min-w-0 flex-1">
                    <div className={cn(
                      "w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0",
                      "bg-gradient-to-r", item.color,
                      "shadow-sm group-hover:shadow-md transition-shadow"
                    )}>
                      <IconComponent className="w-5 h-5 text-white" />
                    </div>
                    <Badge variant="secondary" className="text-xs flex-shrink-0">
                      {item.category}
                    </Badge>
                  </div>
                  <ArrowRight className="w-4 h-4 text-gray-400 group-hover:text-gray-600 transition-colors flex-shrink-0" />
                </div>

                {/* Title */}
                <h3 className="font-semibold text-gray-900 group-hover:text-indigo-600 transition-colors w-full break-words flex-1 flex items-center">
                  {item.title}
                </h3>
              </Button>
            )
          })}
        </div>

        {/* Additional CTA */}
        <div className="text-center">
          <p className="text-sm text-gray-500 mb-4">
            Or start typing your own question in the chat box below
          </p>
          <div className="flex items-center justify-center space-x-2 text-xs text-gray-400">
            <span>💡 Tip: Be specific for better results</span>
            <span>•</span>
            <span>🔒 Your conversations are private</span>
          </div>
        </div>
      </div>
    </div>
  )
}
