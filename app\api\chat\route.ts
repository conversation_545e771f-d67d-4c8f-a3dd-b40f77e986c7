import { NextRequest, NextResponse } from 'next/server';
import { generateContent, generateContentStream, validateVertexAIConfig, VERTEX_AI_MODELS, type VertexAIModel, type ChatMessage } from '@/lib/vertexai';

export const runtime = 'nodejs';

interface ChatRequest {
  message: string;
  model: VertexAIModel;
  history?: Array<{
    role: 'user' | 'assistant';
    content: string;
  }>;
  stream?: boolean;
}

export async function POST(request: NextRequest) {
  try {
    // Validate Vertex AI configuration
    if (!validateVertexAIConfig()) {
      return NextResponse.json(
        { 
          error: 'Vertex AI is not properly configured. Please check your environment variables.' 
        },
        { status: 500 }
      );
    }

    const body: ChatRequest = await request.json();
    const { message, model, history = [], stream = false } = body;

    // Validate request
    if (!message || !message.trim()) {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      );
    }

    if (!Object.values(VERTEX_AI_MODELS).includes(model)) {
      return NextResponse.json(
        { error: 'Invalid model specified' },
        { status: 400 }
      );
    }

    // Convert chat history to Vertex AI format
    const messages: ChatMessage[] = [];
    
    // Add conversation history
    for (const msg of history) {
      messages.push({
        role: msg.role === 'assistant' ? 'model' : 'user',
        parts: [{ text: msg.content }],
      });
    }

    // Add current user message
    messages.push({
      role: 'user',
      parts: [{ text: message }],
    });

    // System instruction for better responses
    const systemInstruction = `You are a helpful AI assistant. Provide clear, accurate, and helpful responses. 
    Be concise but thorough when needed. If you're unsure about something, say so rather than guessing.`;

    if (stream) {
      // Handle streaming response
      const encoder = new TextEncoder();
      
      const stream = new ReadableStream({
        async start(controller) {
          try {
            for await (const chunk of generateContentStream({
              model,
              messages,
              systemInstruction,
            })) {
              const data = `data: ${JSON.stringify({ content: chunk })}\n\n`;
              controller.enqueue(encoder.encode(data));
            }
            
            // Send end signal
            controller.enqueue(encoder.encode('data: [DONE]\n\n'));
            controller.close();
          } catch (error) {
            console.error('Streaming error:', error);
            const errorData = `data: ${JSON.stringify({ 
              error: error instanceof Error ? error.message : 'Unknown error' 
            })}\n\n`;
            controller.enqueue(encoder.encode(errorData));
            controller.close();
          }
        },
      });

      return new Response(stream, {
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
        },
      });
    } else {
      // Handle regular response
      const response = await generateContent({
        model,
        messages,
        systemInstruction,
      });

      return NextResponse.json({
        content: response.content,
        model,
        finishReason: response.finishReason,
      });
    }
  } catch (error) {
    console.error('Chat API error:', error);
    
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'An unexpected error occurred' 
      },
      { status: 500 }
    );
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
