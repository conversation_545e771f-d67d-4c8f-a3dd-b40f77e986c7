import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  updateProfile,
  User,
  setPersistence,
  browserSessionPersistence,
  browserLocalPersistence
} from 'firebase/auth'
import { auth } from './firebase'
import { createUserDocument } from './firestore'

// Types for form data
export interface SignUpData {
  fullName: string
  email: string
  password: string
  agreeToTerms: boolean
}

export interface SignInData {
  email: string
  password: string
  rememberMe: boolean
}

// Custom error type for better error handling
export interface AuthError {
  code: string
  message: string
}

// Form validation functions
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export const validatePassword = (password: string): { isValid: boolean; message?: string } => {
  if (password.length < 6) {
    return { isValid: false, message: 'Password must be at least 6 characters long' }
  }
  if (!/(?=.*[a-z])/.test(password)) {
    return { isValid: false, message: 'Password must contain at least one lowercase letter' }
  }
  if (!/(?=.*[A-Z])/.test(password)) {
    return { isValid: false, message: 'Password must contain at least one uppercase letter' }
  }
  if (!/(?=.*\d)/.test(password)) {
    return { isValid: false, message: 'Password must contain at least one number' }
  }
  return { isValid: true }
}

export const validateFullName = (fullName: string): boolean => {
  return fullName.trim().length >= 2
}

// Firebase Auth error message mapping
export const getAuthErrorMessage = (errorCode: string): string => {
  switch (errorCode) {
    case 'auth/email-already-in-use':
      return 'An account with this email already exists. Please sign in instead.'
    case 'auth/invalid-email':
      return 'Please enter a valid email address.'
    case 'auth/operation-not-allowed':
      return 'Email/password accounts are not enabled. Please contact support.'
    case 'auth/weak-password':
      return 'Password is too weak. Please choose a stronger password.'
    case 'auth/user-disabled':
      return 'This account has been disabled. Please contact support.'
    case 'auth/user-not-found':
      return 'No account found with this email address.'
    case 'auth/wrong-password':
      return 'Incorrect password. Please try again.'
    case 'auth/too-many-requests':
      return 'Too many failed attempts. Please try again later.'
    case 'auth/network-request-failed':
      return 'Network error. Please check your connection and try again.'
    default:
      return 'An unexpected error occurred. Please try again.'
  }
}

// Authentication functions
export const signUpWithEmailAndPassword = async (
  email: string,
  password: string,
  fullName: string
): Promise<User> => {
  try {
    // Create user with Firebase Auth
    const userCredential = await createUserWithEmailAndPassword(auth, email, password)

    // Update the user's display name in Firebase Auth
    await updateProfile(userCredential.user, {
      displayName: fullName
    })

    // Create user document in Firestore
    await createUserDocument(userCredential.user, fullName)

    return userCredential.user
  } catch (error: any) {
    throw {
      code: error.code,
      message: getAuthErrorMessage(error.code)
    } as AuthError
  }
}

export const signInWithEmail = async (email: string, password: string, rememberMe: boolean = false): Promise<User> => {
  try {
    // Set persistence based on rememberMe preference
    if (rememberMe) {
      // Remember for 30 days - use local storage persistence
      await setPersistence(auth, browserLocalPersistence)
    } else {
      // Session only - sign out when browser/tab is closed
      await setPersistence(auth, browserSessionPersistence)
    }

    const userCredential = await signInWithEmailAndPassword(auth, email, password)

    // Store session data for tracking remember me preference
    setSessionData(rememberMe)

    return userCredential.user
  } catch (error: any) {
    throw {
      code: error.code,
      message: getAuthErrorMessage(error.code)
    } as AuthError
  }
}

export const logOut = async (): Promise<void> => {
  try {
    await signOut(auth)
    // Clear any session storage data
    clearSessionData()
  } catch (error: any) {
    throw {
      code: error.code,
      message: getAuthErrorMessage(error.code)
    } as AuthError
  }
}

// Session management utilities
export const setSessionData = (rememberMe: boolean): void => {
  if (typeof window !== 'undefined') {
    const sessionData = {
      rememberMe,
      timestamp: Date.now()
    }
    sessionStorage.setItem('auth_session', JSON.stringify(sessionData))
  }
}

export const getSessionData = (): { rememberMe: boolean; timestamp: number } | null => {
  if (typeof window !== 'undefined') {
    const data = sessionStorage.getItem('auth_session')
    if (data) {
      try {
        return JSON.parse(data)
      } catch {
        return null
      }
    }
  }
  return null
}

export const clearSessionData = (): void => {
  if (typeof window !== 'undefined') {
    sessionStorage.removeItem('auth_session')
  }
}

// Check if session should be maintained based on remember me preference
export const shouldMaintainSession = (): boolean => {
  const sessionData = getSessionData()
  if (!sessionData) return false

  // If remember me was checked, maintain session
  if (sessionData.rememberMe) return true

  // If remember me was not checked, session should only last for the browser session
  // This is handled by Firebase's browserSessionPersistence
  return false
}

// Initialize auth persistence on app start
export const initializeAuthPersistence = async (): Promise<void> => {
  try {
    // Check if user had remember me enabled in previous session
    const sessionData = getSessionData()
    if (sessionData && sessionData.rememberMe) {
      await setPersistence(auth, browserLocalPersistence)
    } else {
      await setPersistence(auth, browserSessionPersistence)
    }
  } catch (error) {
    console.error('Error initializing auth persistence:', error)
    // Fallback to session persistence if there's an error
    try {
      await setPersistence(auth, browserSessionPersistence)
    } catch (fallbackError) {
      console.error('Error setting fallback persistence:', fallbackError)
    }
  }
}
