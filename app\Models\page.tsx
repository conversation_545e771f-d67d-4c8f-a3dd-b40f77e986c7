"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Brain,
  Zap,
  MessageSquare,
  Image,
  Code,
  FileText,
  Sparkles,
  ArrowLeft,
  Star,
  Clock,
  Layers,
  Cpu,
  Video,
  Music,
  Camera
} from "lucide-react"
import Link from "next/link"

export default function ModelsPage() {
  const [selectedCategory, setSelectedCategory] = useState("all")

  const modelCategories = [
    { id: "all", name: "All Models", icon: Layers },
    { id: "gemini", name: "<PERSON>", icon: Brain },
    { id: "chatgpt", name: "ChatGPT", icon: MessageSquare },
    { id: "claude", name: "<PERSON>", icon: Sparkles },
    { id: "mistral", name: "Mistra<PERSON>", icon: Zap },
    { id: "grok", name: "<PERSON><PERSON>", icon: <PERSON><PERSON> },
    { id: "deepseek", name: "DeepSeek", icon: Code },
    { id: "media", name: "Media Generation", icon: Camera },
  ]

  const models = [
    // Gemini Models
    {
      id: "gemini-2.5-flash-preview-05-20",
      name: "Gemini 2.5 Flash Preview",
      category: "gemini",
      provider: "Google",
      description: "Latest preview version of Gemini 2.5 Flash with enhanced capabilities and improved performance. This cutting-edge model offers the newest features and optimizations from Google's Vertex AI platform.",
      capabilities: ["Text", "Code", "Analysis", "Fast responses"],
      contextLength: "1M tokens",
      tier: "Pro",
      gradient: "from-blue-600 to-cyan-600",
      features: [
        "Latest preview features and improvements",
        "Enhanced reasoning capabilities",
        "Optimized for speed and efficiency",
        "Advanced code generation and debugging",
        "Improved instruction following",
        "State-of-the-art performance benchmarks"
      ],
      pricing: "Pro tier",
      performance: "Highest",
      latency: "Very Fast"
    },
    {
      id: "gemini-ultra",
      name: "Gemini 2.5 Pro",
      category: "gemini",
      provider: "Google",
      description: "A highly advanced multimodal language model from Google, designed for complex tasks like reasoning, coding, and understanding visual information. It's positioned as Google's most powerful thinking model with maximum response accuracy and state-of-the-art performance.",
      capabilities: ["Text", "Images", "Video", "Audio", "Code", "Math"],
      contextLength: "1M tokens",
      tier: "Pro",
      gradient: "from-blue-500 to-cyan-500",
      features: [
        "Advanced multimodal understanding",
        "Complex reasoning and analysis (thinking process before responding)",
        "Code generation and debugging",
        "Mathematical problem solving",
        "Supports thinking budgets for controlling quality, cost, and latency",
        "Native audio output and improvements to Live API"
      ],
      pricing: "Premium tier",
      performance: "Highest",
      latency: "Medium"
    },
    {
      id: "gemini-pro",
      name: "Gemini 2.5 Flash",
      category: "gemini",
      provider: "Google",
      description: "A well-rounded, thinking model that excels at balancing price and performance. It's designed for speed and cost-efficiency while offering a major upgrade in reasoning capabilities compared to 2.0 Flash. Google's most cost-efficient thinking model.",
      capabilities: ["Text", "Images", "Video", "Audio", "Code"],
      contextLength: "1M tokens",
      tier: "Pro",
      gradient: "from-blue-400 to-cyan-400",
      features: [
        "Multimodal capabilities (natively multimodal)",
        "Fast response times",
        "Code understanding and tool use (Search, code execution)",
        "Image analysis",
        "Fully hybrid reasoning model with configurable thinking",
        "20-30% more efficient token usage"
      ],
      pricing: "Pro tier",
      performance: "High",
      latency: "Fast"
    },
    {
      id: "gemini-flash",
      name: "Gemini 2.0 Flash",
      category: "gemini",
      provider: "Google",
      description: "Designed for speed, efficiency, and improved performance across various tasks. It's a workhorse model with low latency and enhanced performance, popular with developers for high-volume, high-frequency tasks.",
      capabilities: ["Text", "Images", "Video", "Audio", "Code"],
      contextLength: "1M tokens",
      tier: "Starter",
      gradient: "from-blue-300 to-cyan-300",
      features: [
        "Ultra-fast responses (0.53s to first token)",
        "Native multimodal input and output",
        "Native tool use (Google Search, code execution)",
        "Complex instruction following and planning",
        "High output speed (169.5 tokens per second)",
        "Compositional function-calling"
      ],
      pricing: "Starter tier",
      performance: "Good to High",
      latency: "Very Fast"
    },
    {
      id: "gemini-flash-lite",
      name: "Gemini 2.0 Flash-Lite",
      category: "gemini",
      provider: "Google",
      description: "Fastest model optimized for speed and cost-efficient performance.",
      capabilities: ["Text", "Images", "Video", "Audio"],
      contextLength: "1M tokens",
      tier: "Starter",
      gradient: "from-blue-300 to-cyan-300",
      features: [
        "Ultra-fast responses",
        "Efficient processing",
        "Basic multimodal support",
        "Cost-effective"
      ],
      pricing: "Starter tier",
      performance: "Good",
      latency: "Very Fast"
    },
    // ChatGPT Models
    {
      id: "gpt-4-turbo",
      name: "GPT-4.1",
      category: "chatgpt",
      provider: "OpenAI",
      description: "Smartest model for complex tasks, built specifically for coding tasks. Aims for the best overall performance across coding, instruction following, and long-context tasks.",
      capabilities: ["Text", "Images", "Code", "Analysis"],
      contextLength: "1M tokens",
      tier: "Business",
      gradient: "from-green-500 to-emerald-500",
      features: [
        "Advanced reasoning",
        "Vision capabilities (image in, text out)",
        "Code interpretation",
        "Large context window (1 million tokens)",
        "Superior coding abilities",
        "Enhanced instruction following with negative constraints"
      ],
      pricing: "Business tier",
      performance: "Highest",
      latency: "Medium"
    },
    {
      id: "gpt-4",
      name: "GPT-4.1 mini",
      category: "chatgpt",
      provider: "OpenAI",
      description: "Affordable model balancing speed and intelligence. A reduced-size model maintaining performance comparable to GPT-4o while reducing latency. Likely to be a default choice for many use cases.",
      capabilities: ["Text", "Images", "Code", "Analysis"],
      contextLength: "1M tokens",
      tier: "Pro",
      gradient: "from-green-400 to-emerald-400",
      features: [
        "Superior reasoning",
        "Creative writing",
        "Complex problem solving",
        "Accurate information",
        "Large context window (1 million tokens)",
        "Matches or beats GPT-4o on multiple benchmarks"
      ],
      pricing: "Pro tier",
      performance: "High",
      latency: "Medium (50% faster than GPT-4.1)"
    },
    {
      id: "gpt-3-5-turbo",
      name: "GPT-4.1 nano",
      category: "chatgpt",
      provider: "OpenAI",
      description: "Fastest, most cost-effective model for low-latency tasks. Designed for applications where speed and economy are paramount.",
      capabilities: ["Text", "Images", "Code"],
      contextLength: "1M tokens",
      tier: "Starter",
      gradient: "from-green-300 to-emerald-300",
      features: [
        "Quick responses (OpenAI's fastest model)",
        "Good for conversations",
        "Code assistance",
        "Cost-effective",
        "Large context window (1 million tokens)",
        "Ideal for classification, autocomplete, and data extraction"
      ],
      pricing: "Starter tier",
      performance: "Good",
      latency: "Fast (50% faster than GPT-4o)"
    },
    {
      id: "OpenAI o4-mini",
      name: "OpenAI o4-mini",
      category: "chatgpt",
      provider: "OpenAI",
      description: "A faster, cost-efficient reasoning model delivering strong performance on math, coding, and vision. A compact reasoning model in the o-series, optimized for fast, cost-efficient performance while retaining strong multimodal and agentic capabilities.",
      capabilities: ["Text", "Code", "Images"],
      contextLength: "200K tokens",
      tier: "Starter",
      gradient: "from-green-300 to-emerald-300",
      features: [
        "Quick responses",
        "Good for conversations",
        "Code assistance",
        "Cost-effective",
        "Tool use (web Browse, Python code execution, image analysis)",
        "Can chain tools and generate structured outputs"
      ],
      pricing: "Starter tier",
      performance: "Good",
      latency: "Fast"
    },
    {
      id: "OpenAI o3",
      name: "OpenAI o3",
      category: "chatgpt",
      provider: "OpenAI",
      description: "OpenAI's most powerful reasoning model with leading performance on coding, math, science, and vision. Designed to devote additional deliberation time for step-by-step logical reasoning using a private chain of thought.",
      capabilities: ["Text", "Code", "Images"],
      contextLength: "200K tokens",
      tier: "Business",
      gradient: "from-green-500 to-emerald-500",
      features: [
        "Enhanced problem-solving (breaks down complex problems)",
        "Improved logical reasoning",
        "Improved memory for long-term dependencies",
        "Highly customizable",
        "Energy-efficient",
        "Stable performance in chain-of-thought reasoning"
      ],
      pricing: "Business tier",
      performance: "Good",
      latency: "Fast (comparable to GPT-3.5)"
    },
    // Claude Models
    {
      id: "claude-opus",
      name: "Claude Opus 4",
      category: "claude",
      provider: "Anthropic",
      description: "Most powerful Claude model for complex tasks. Demonstrates strong performance on coding and agent-focused benchmarks.",
      capabilities: ["Text", "Images", "Code", "Analysis"],
      contextLength: "200K tokens",
      tier: "Business",
      gradient: "from-purple-500 to-pink-500",
      features: [
        "Exceptional reasoning and problem-solving",
        "Nuanced understanding",
        "Creative capabilities",
        "Ethical AI responses",
        "Enhanced safety and steerability",
        "Hybrid reasoning model with near-instant responses and extended thinking"
      ],
      pricing: "Business tier",
      performance: "Highest",
      latency: "Medium"
    },
    {
      id: "claude-sonnet",
      name: "Claude Sonnet 4",
      category: "claude",
      provider: "Anthropic",
      description: "Balanced model for most applications, complementing Opus 4 by balancing performance, responsiveness, and cost. Optimized for everyday development tasks and high-volume production workloads.",
      capabilities: ["Text", "Images", "Code"],
      contextLength: "200K tokens",
      tier: "Business",
      gradient: "from-purple-400 to-pink-400",
      features: [
        "Strong performance",
        "Reliable outputs",
        "Good reasoning",
        "Helpful responses",
        "Optimized for throughput and latency-sensitive applications",
        "Intelligence comparable to or exceeding Claude 3 Opus"
      ],
      pricing: "Business tier",
      performance: "High",
      latency: "Fast"
    },
    {
      id: "claude-haiku",
      name: "Claude Sonnet 3.7",
      category: "claude",
      provider: "Anthropic",
      description: "Predecessor to Claude Sonnet 4. This seems to be a slight misattribution - Claude Sonnet 3.7 is the predecessor to Claude Sonnet 4.",
      capabilities: ["Text", "Code"],
      contextLength: "200K tokens",
      tier: "Pro",
      gradient: "from-purple-300 to-pink-300",
      features: [
        "Lightning fast",
        "Efficient processing",
        "Good for simple tasks",
        "Budget-friendly"
      ],
      pricing: "Pro tier",
      performance: "Good",
      latency: "Very Fast"
    },
    {
      id: "claude-haikuu",
      name: "Claude Haiku 3.5",
      category: "claude",
      provider: "Anthropic",
      description: "Fastest Claude model for quick tasks, bringing increased accuracy and surpassing Claude 3 Opus in certain contexts. Combines rapid response times with improved reasoning.",
      capabilities: ["Text", "Code"],
      contextLength: "200K tokens",
      tier: "Pro",
      gradient: "from-purple-300 to-pink-300",
      features: [
        "Lightning fast (generates 65.2 tokens per second)",
        "Efficient processing",
        "Enhanced conversational abilities",
        "Improved instruction following and tool use",
        "Well-suited for user-facing products and real-time content moderation",
        "60% faster inference on Bedrock"
      ],
      pricing: "Pro tier",
      performance: "Good",
      latency: "Very Fast"
    },
    // Mistral Models
    {
      id: "mistral-large",
      name: "Mistral Large",
      category: "mistral",
      provider: "Mistral AI",
      description: "Most capable Mistral model for complex reasoning.",
      capabilities: ["Text", "Code", "Analysis"],
      contextLength: "128K tokens",
      tier: "Pro",
      gradient: "from-orange-500 to-red-500",
      features: [
        "Advanced reasoning",
        "Multilingual support",
        "Code generation",
        "Mathematical capabilities"
      ],
      pricing: "Pro tier",
      performance: "Highest",
      latency: "Medium"
    },
    {
      id: "mistral-medium",
      name: "Mistral Medium",
      category: "mistral",
      provider: "Mistral AI",
      description: "Balanced performance for general use.",
      capabilities: ["Text", "Code"],
      contextLength: "128K tokens",
      tier: "Pro",
      gradient: "from-orange-400 to-red-400",
      features: [
        "Good performance",
        "Efficient processing",
        "Code understanding",
        "Versatile applications"
      ],
      pricing: "Pro tier",
      performance: "High",
      latency: "Fast"
    },
    {
      id: "mistral-small",
      name: "Mistral Small 3.1",
      category: "mistral",
      provider: "Mistral AI",
      description: "Efficient model for everyday tasks, optimized for high-volume, low-latency language-based tasks. The best model in its weight class, with improved text performance, multimodal understanding, and an expanded context window.",
      capabilities: ["Text", "Images"],
      contextLength: "128K tokens",
      tier: "Starter",
      gradient: "from-orange-300 to-red-300",
      features: [
        "Fast responses (150 tokens per second)",
        "Cost-effective",
        "Good for chat",
        "Lightweight",
        "Retrieval-Augmented Generation (RAG) specialization",
        "First Mistral model to accept images with text"
      ],
      pricing: "Starter tier",
      performance: "Good",
      latency: "Very Fast"
    },

    // Grok Models
    {
      id: "grok-2",
      name: "Grok 2",
      category: "grok",
      provider: "xAI",
      description: "Advanced reasoning model with real-time information access and multimodal capabilities. Grok 2 is xAI's flagship model designed for complex reasoning, coding, and real-time web search integration.",
      capabilities: ["Text", "Images", "Code", "Real-time Search", "Analysis"],
      contextLength: "128K tokens",
      tier: "Pro",
      gradient: "from-gray-600 to-black",
      features: [
        "Real-time web search integration",
        "Advanced reasoning and problem-solving",
        "Multimodal understanding (text and images)",
        "Code generation and debugging",
        "Up-to-date information access",
        "Conversational AI with personality"
      ],
      pricing: "Business tier",
      performance: "Highest",
      latency: "Medium"
    },
    {
      id: "grok-2-mini",
      name: "grok-3",
      category: "grok",
      provider: "xAI",
      description: "Excels at enterprise use cases like data extraction, coding, and text summarization. Possesses deep domain knowledge in finance, healthcare, law, and science.",
      capabilities: ["Text", "Images", "Code", "Real-time Search"],
      contextLength: "128K tokens",
      tier: "Business",
      gradient: "from-gray-500 to-gray-700",
      features: [
        "Fast response times",
        "Real-time information access",
        "Efficient processing",
        "Good reasoning capabilities",
        "Cost-effective",
        "Multimodal support"
      ],
      pricing: "Pro tier",
      performance: "High",
      latency: "Fast"
    },

    // DeepSeek Models
    {
      id: "deepseek-v3",
      name: "DeepSeek V3",
      category: "deepseek",
      provider: "DeepSeek",
      description: "State-of-the-art reasoning model with exceptional performance in mathematics, coding, and complex problem-solving. DeepSeek V3 represents the latest advancement in open-source AI models.",
      capabilities: ["Text", "Code", "Math", "Analysis", "Reasoning"],
      contextLength: "128K tokens",
      tier: "Pro",
      gradient: "from-blue-600 to-indigo-800",
      features: [
        "Exceptional mathematical reasoning",
        "Advanced code generation and debugging",
        "Complex problem-solving capabilities",
        "Strong logical reasoning",
        "Competitive with leading proprietary models",
        "Open-source architecture"
      ],
      pricing: "Business tier",
      performance: "Highest",
      latency: "Medium"
    },
    {
      id: "deepseek-r1",
      name: "DeepSeek R1",
      category: "deepseek",
      provider: "DeepSeek",
      description: "Reasoning-focused model with enhanced chain-of-thought capabilities, designed for complex analytical tasks and step-by-step problem solving.",
      capabilities: ["Text", "Reasoning", "Analysis", "Math"],
      contextLength: "128K tokens",
      tier: "Pro",
      gradient: "from-blue-400 to-indigo-500",
      features: [
        "Enhanced chain-of-thought reasoning",
        "Step-by-step problem breakdown",
        "Analytical thinking capabilities",
        "Mathematical problem solving",
        "Logical deduction",
        "Transparent reasoning process"
      ],
      pricing: "Pro tier",
      performance: "High",
      latency: "Medium"
    },

    // Media Generation Models
    {
      id: "imagen-3",
      name: "Imagen 3.0",
      category: "media",
      provider: "Google",
      description: "Advanced image generation model with photorealistic quality.",
      capabilities: ["Images", "Text-to-Image"],
      contextLength: "N/A",
      tier: "Pro",
      gradient: "from-pink-500 to-rose-500",
      features: [
        "Photorealistic image generation",
        "High-resolution outputs",
        "Style control and customization",
        "Fast generation times"
      ],
      pricing: "Pro tier",
      performance: "Highest",
      latency: "Fast"
    },
    {
      id: "gemini-2-flash-image",
      name: "Gemini 2.0 Flash Image Generation",
      category: "media",
      provider: "Google",
      description: "Fast image generation powered by Gemini 2.0 Flash, offering improved image generation features, including conversational image generation and editing.",
      capabilities: ["Images", "Text-to-Image"],
      contextLength: "2M tokens",
      tier: "Pro",
      gradient: "from-blue-500 to-purple-500",
      features: [
        "Quick image generation (0.53 seconds average latency)",
        "Integrated with Gemini ecosystem",
        "Real-time processing and object recognition",
        "Image editing with natural language",
        "Advanced composition and style control",
        "SynthID integration for watermarking"
      ],
      pricing: "Starter tier",
      performance: "High",
      latency: "Very Fast"
    },
    {
      id: "veo-2",
      name: "Veo 2",
      category: "media",
      provider: "Google",
      description: "State-of-the-art video generation model for creating high-quality videos.",
      capabilities: ["Video", "Text-to-Video"],
      contextLength: "N/A",
      tier: "Business",
      gradient: "from-indigo-500 to-blue-500",
      features: [
        "High-quality video generation",
        "Multiple video formats",
        "Advanced motion control",
        "Long-form video support"
      ],
      pricing: "Premium tier",
      performance: "Highest",
      latency: "Medium"
    },
    {
      id: "lyria-realtime",
      name: "Lyria RealTime",
      category: "media",
      provider: "Google",
      description: "Real-time audio generation and synthesis model.",
      capabilities: ["Audio", "Text-to-Speech", "Music"],
      contextLength: "N/A",
      tier: "Business",
      gradient: "from-green-500 to-teal-500",
      features: [
        "Real-time audio synthesis",
        "Music generation",
        "Voice cloning capabilities",
        "Multiple audio formats"
      ],
      pricing: "Pro tier",
      performance: "High",
      latency: "Real-time"
    },
  ]

  const filteredModels = selectedCategory === "all"
    ? models
    : models.filter(model => model.category === selectedCategory)

  const getCapabilityIcon = (capability: string) => {
    switch (capability.toLowerCase()) {
      case "text": return MessageSquare
      case "images": return Image
      case "code": return Code
      case "analysis": return Brain
      case "math": return Cpu
      case "video": return Video
      case "audio": return Music
      case "text-to-image": return Camera
      case "text-to-video": return Video
      case "text-to-speech": return Music
      case "music": return Music
      case "real-time search": return Zap
      case "reasoning": return Brain
      case "debugging": return Code
      default: return FileText
    }
  }

  const getTierColor = (tier: string) => {
    switch (tier) {
      case "Premium": return "bg-gradient-to-r from-purple-500 to-pink-500 text-white"
      case "Business": return "bg-gradient-to-r from-yellow-400 to-orange-500 text-white"
      case "Pro": return "bg-gradient-to-r from-blue-500 to-purple-600 text-white"
      case "Starter": return "bg-gradient-to-r from-green-400 to-blue-500 text-white"
      default: return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="fixed top-0 w-full bg-white/80 backdrop-blur-md border-b border-gray-100 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Link href="/" className="flex items-center space-x-2 hover:opacity-80 transition-opacity">
                <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center">
                  <Sparkles className="w-5 h-5 text-white" />
                </div>
                <span className="text-xl font-bold text-gray-900">Promptly Models</span>
              </Link>
            </div>

            <div className="flex items-center space-x-4">
              <Button variant="ghost">Login</Button>
              <Button>Sign Up</Button>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-24 pb-16 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-indigo-50 via-white to-cyan-50" />
        <div className="absolute inset-0 opacity-40">
          <div className="absolute inset-0 bg-gradient-to-r from-indigo-100/20 to-purple-100/20" />
          <div
            className="absolute inset-0"
            style={{
              backgroundImage: "radial-gradient(circle at 1px 1px, rgba(99, 102, 241, 0.1) 1px, transparent 0)",
              backgroundSize: "20px 20px",
            }}
          />
        </div>

        <div className="max-w-7xl mx-auto relative">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              Choose Your{" "}
              <span className="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                AI Model
              </span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              Access the world's most advanced AI models in one platform. From Google's Gemini to OpenAI's GPT-4,
              Anthropic's Claude, Mistral's cutting-edge models, xAI's Grok, DeepSeek's reasoning models, and powerful media generation tools like Imagen, Veo, and Lyria.
            </p>
          </div>
        </div>
      </section>

      {/* Category Filters */}
      <section className="py-8 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-wrap justify-center gap-4">
            {modelCategories.map((category) => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? "default" : "outline"}
                onClick={() => setSelectedCategory(category.id)}
                className={`flex items-center space-x-2 ${
                  selectedCategory === category.id
                    ? "bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700"
                    : ""
                }`}
              >
                <category.icon className="w-4 h-4" />
                <span>{category.name}</span>
              </Button>
            ))}
          </div>
        </div>
      </section>

      {/* Models Grid */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredModels.map((model) => (
              <Card key={model.id} className="h-full border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                <CardHeader>
                  <div className="flex items-start justify-between mb-4">
                    <div
                      className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${model.gradient} flex items-center justify-center`}
                    >
                      <Brain className="w-8 h-8 text-white" />
                    </div>
                    <Badge className={getTierColor(model.tier)}>
                      {model.tier}
                    </Badge>
                  </div>

                  <div className="space-y-2">
                    <CardTitle className="text-2xl">{model.name}</CardTitle>
                    <div className="text-sm text-gray-500 font-medium">{model.provider}</div>
                    <CardDescription className="text-base">{model.description}</CardDescription>
                  </div>

                  {/* Capabilities */}
                  <div className="flex flex-wrap gap-2 mt-4">
                    {model.capabilities.map((capability) => {
                      const IconComponent = getCapabilityIcon(capability)
                      return (
                        <div
                          key={capability}
                          className="flex items-center space-x-1 bg-gray-100 rounded-full px-3 py-1 text-sm text-gray-700"
                        >
                          <IconComponent className="w-3 h-3" />
                          <span>{capability}</span>
                        </div>
                      )
                    })}
                  </div>

                  {/* Context Length */}
                  <div className="flex items-center space-x-2 mt-4 text-sm text-gray-600">
                    <FileText className="w-4 h-4" />
                    <span>Context: {model.contextLength}</span>
                  </div>
                </CardHeader>

                <CardContent>
                  {/* Features */}
                  <div className="space-y-3 mb-6">
                    <h4 className="font-semibold text-gray-900">Key Features</h4>
                    <ul className="space-y-2">
                      {model.features.map((feature, index) => (
                        <li key={index} className="flex items-start space-x-2 text-sm text-gray-600">
                          <div className="w-1.5 h-1.5 bg-indigo-500 rounded-full mt-2 flex-shrink-0" />
                          <span>{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Performance Metrics */}
                  <div className="grid grid-cols-2 gap-4 mb-6 text-sm">
                    <div className="space-y-1">
                      <div className="text-gray-500">Performance</div>
                      <div className="font-semibold text-gray-900">{model.performance}</div>
                    </div>
                    <div className="space-y-1">
                      <div className="text-gray-500">Speed</div>
                      <div className="font-semibold text-gray-900">{model.latency}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center">
                  <Sparkles className="w-5 h-5 text-white" />
                </div>
                <span className="text-xl font-bold">Promptly</span>
              </div>
              <p className="text-gray-400 mb-4">
                One platform for all your AI needs. Built with the world's leading AI models.
              </p>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 Promptly. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}