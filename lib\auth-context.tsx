"use client"

import React, { createContext, useContext, useEffect, useState } from 'react'
import { User, onAuthStateChanged } from 'firebase/auth'
import { auth } from './firebase'
import { getUserDocument, UserDocument } from './firestore'
import { getSessionData, logOut, initializeAuthPersistence } from './auth'

interface AuthContextType {
  user: User | null
  userDocument: UserDocument | null
  loading: boolean
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  userDocument: null,
  loading: true,
})

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: React.ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [userDocument, setUserDocument] = useState<UserDocument | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Initialize auth persistence on app start
    initializeAuthPersistence()

    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setUser(user)

      if (user) {
        try {
          // Fetch user document from Firestore
          const userDoc = await getUserDocument(user.uid)
          setUserDocument(userDoc)
        } catch (error) {
          console.error('Error fetching user document:', error)
          setUserDocument(null)
        }
      } else {
        setUserDocument(null)
      }

      setLoading(false)
    })

    return () => unsubscribe()
  }, [])

  // Handle page visibility change for session management
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && user) {
        // Check if session should be maintained when page becomes visible
        const sessionData = getSessionData()
        if (sessionData && !sessionData.rememberMe) {
          // For non-remembered sessions, check if this is a new browser session
          // If the user didn't check "remember me", they should be signed out
          // when they close and reopen the browser
          const sessionStart = sessionStorage.getItem('session_start')
          if (!sessionStart) {
            // This indicates a new browser session, sign out the user
            logOut().catch(console.error)
          }
        }
      }
    }

    // Set session start marker
    if (typeof window !== 'undefined' && user) {
      const sessionData = getSessionData()
      if (sessionData && !sessionData.rememberMe) {
        sessionStorage.setItem('session_start', Date.now().toString())
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange)
  }, [user])

  const value = {
    user,
    userDocument,
    loading,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
