# Vertex AI Integration Setup

This document explains how to set up and configure Google Cloud Vertex AI integration for the Promptly application.

## Prerequisites

1. **Google Cloud Project**: You need a Google Cloud Project with billing enabled
2. **Vertex AI API**: Enable the Vertex AI API in your Google Cloud Console
3. **Service Account**: Create a service account with appropriate permissions

## Setup Steps

### 1. Enable Vertex AI API

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project
3. Navigate to "APIs & Services" > "Library"
4. Search for "Vertex AI API" and enable it

### 2. Create Service Account

1. Go to "IAM & Admin" > "Service Accounts"
2. Click "Create Service Account"
3. Give it a name like "promptly-vertexai"
4. Assign the following roles:
   - `Vertex AI User`
   - `AI Platform Developer` (if needed)

### 3. Generate Service Account Key

1. Click on your service account
2. Go to "Keys" tab
3. Click "Add Key" > "Create new key"
4. Choose JSON format
5. Download the key file

### 4. Configure Environment Variables

Create a `.env.local` file in your project root with the following variables:

```env
# Google Cloud / Vertex AI Configuration
GOOGLE_CLOUD_PROJECT_ID=your_google_cloud_project_id
GOOGLE_CLOUD_LOCATION=us-central1
GOOGLE_APPLICATION_CREDENTIALS=path/to/your/service-account-key.json

# Alternative for deployment (use the JSON content directly)
# GOOGLE_CLOUD_CREDENTIALS={"type":"service_account","project_id":"..."}
```

### 5. Available Models

The integration currently supports these Vertex AI models:

- **gemini-2.5-flash-preview-05-20**: Latest preview version with enhanced capabilities
- **gemini-2.5-pro**: Most capable model for complex tasks
- **gemini-2.5-flash**: Fast and efficient model

## Usage

### Basic Chat

The chat interface automatically uses the selected Vertex AI model. Users can:

1. Select a model from the dropdown
2. Send messages
3. Receive AI-generated responses

### API Endpoint

The chat API is available at `/api/chat` with the following structure:

```typescript
POST /api/chat
{
  "message": "Your message here",
  "model": "gemini-2.5-flash-preview-05-20",
  "history": [
    {
      "role": "user",
      "content": "Previous message"
    },
    {
      "role": "assistant", 
      "content": "Previous response"
    }
  ],
  "stream": false
}
```

### Response Format

```typescript
{
  "content": "AI response text",
  "model": "gemini-2.5-flash-preview-05-20",
  "finishReason": "STOP"
}
```

## Configuration Options

### Model Parameters

You can adjust model parameters in `lib/vertexai.ts`:

- `temperature`: Controls randomness (0.0 to 1.0)
- `topP`: Controls diversity (0.0 to 1.0)
- `topK`: Limits token selection (1 to 40)
- `maxOutputTokens`: Maximum response length

### System Instructions

The system instruction can be customized in the API route to change the AI's behavior and personality.

## Troubleshooting

### Common Issues

1. **Authentication Error**: Ensure your service account key is valid and has proper permissions
2. **Project ID Error**: Verify your Google Cloud Project ID is correct
3. **API Not Enabled**: Make sure Vertex AI API is enabled in your project
4. **Quota Exceeded**: Check your API quotas in Google Cloud Console

### Error Messages

- `Vertex AI is not properly configured`: Check environment variables
- `Invalid model specified`: Ensure you're using a supported model ID
- `Failed to generate content`: Check API permissions and quotas

## Security Notes

- Never commit service account keys to version control
- Use environment variables for all sensitive configuration
- Consider using Google Cloud's Application Default Credentials in production
- Implement proper rate limiting and user authentication

## Cost Considerations

- Vertex AI charges per token (input and output)
- Monitor usage in Google Cloud Console
- Consider implementing usage limits per user
- Use the most cost-effective model for your use case

## Next Steps

1. Test the integration with a simple message
2. Implement streaming responses for better UX
3. Add error handling and retry logic
4. Monitor usage and costs
5. Consider adding more models as needed
