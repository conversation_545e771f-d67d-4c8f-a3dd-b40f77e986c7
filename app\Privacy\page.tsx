"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  ArrowLeft,
  Shield,
  AlertTriangle,
  Clock,
  Database,
  Eye,
  Lock,
  Users,
  Globe,
  Cookie,
  FileText,
  Mail,
  ChevronRight,
  UserCheck
} from "lucide-react"
import Link from "next/link"
import { cn } from "@/lib/utils"

export default function PrivacyPolicyPage() {
  const [activeSection, setActiveSection] = useState("")

  // Table of contents data
  const sections = [
    { id: "information-collection", title: "Information We Collect", icon: Database },
    { id: "information-use", title: "How We Use Your Information", icon: Eye },
    { id: "data-sharing", title: "Data Sharing and Disclosure", icon: Users },
    { id: "data-security", title: "Data Security and Protection", icon: Lock },
    { id: "data-retention", title: "Data Retention", icon: Clock },
    { id: "privacy-rights", title: "Your Privacy Rights", icon: User<PERSON>he<PERSON> },
    { id: "cookies-tracking", title: "Cookies and Tracking", icon: <PERSON><PERSON> },
    { id: "third-party-services", title: "Third-Party Services", icon: Globe },
    { id: "international-transfers", title: "International Data Transfers", icon: Globe },
    { id: "children-privacy", title: "Children's Privacy", icon: Shield },
    { id: "policy-changes", title: "Changes to Privacy Policy", icon: FileText },
    { id: "contact", title: "Contact Information", icon: Mail }
  ]

  // Scroll spy effect
  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY + 100

      for (const section of sections) {
        const element = document.getElementById(section.id)
        if (element) {
          const offsetTop = element.offsetTop
          const offsetBottom = offsetTop + element.offsetHeight

          if (scrollPosition >= offsetTop && scrollPosition < offsetBottom) {
            setActiveSection(section.id)
            break
          }
        }
      }
    }

    window.addEventListener("scroll", handleScroll)
    handleScroll() // Initial call

    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: "smooth", block: "start" })
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link href="/">
                <Button variant="ghost" size="sm" className="hover:bg-gray-100">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Home
                </Button>
              </Link>
              <div className="h-6 w-px bg-gray-300" />
              <div>
                <h1 className="text-lg font-semibold text-gray-900">Privacy Policy</h1>
                <p className="text-sm text-gray-500">Effective Date: January 1, 2024</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className="text-xs">
                <Shield className="w-3 h-3 mr-1" />
                Privacy Document
              </Badge>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid lg:grid-cols-4 gap-8">
          {/* Table of Contents - Sticky Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-24">
              <Card className="border-0 shadow-lg">
                <CardHeader className="pb-4">
                  <CardTitle className="text-lg flex items-center">
                    <Shield className="w-5 h-5 mr-2 text-indigo-600" />
                    Contents
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <nav className="space-y-1">
                    {sections.map((section) => (
                      <button
                        key={section.id}
                        onClick={() => scrollToSection(section.id)}
                        className={cn(
                          "w-full text-left px-3 py-2 rounded-lg text-sm transition-all duration-200 flex items-center group",
                          activeSection === section.id
                            ? "bg-gradient-to-r from-indigo-50 to-purple-50 text-indigo-700 border-l-2 border-indigo-500"
                            : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                        )}
                      >
                        <section.icon className={cn(
                          "w-4 h-4 mr-2 flex-shrink-0",
                          activeSection === section.id ? "text-indigo-600" : "text-gray-400"
                        )} />
                        <span className="flex-1">{section.title}</span>
                        <ChevronRight className={cn(
                          "w-3 h-3 transition-transform",
                          activeSection === section.id ? "rotate-90 text-indigo-600" : "text-gray-400 group-hover:translate-x-1"
                        )} />
                      </button>
                    ))}
                  </nav>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            <div className="space-y-8">
              {/* Introduction */}
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center">
                      <Shield className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <CardTitle className="text-2xl">Privacy Policy</CardTitle>
                      <p className="text-gray-600 mt-1">Your Privacy Matters to Us</p>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="prose prose-gray max-w-none">
                    <p className="text-lg text-gray-700 leading-relaxed">
                      At Promptly, we are committed to protecting your privacy and ensuring the security of your personal information.
                      This Privacy Policy explains how we collect, use, and safeguard your data when you use our AI platform.
                    </p>
                    <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-xl">
                      <div className="flex items-start space-x-3">
                        <AlertTriangle className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                        <div>
                          <h4 className="font-semibold text-blue-900 mb-1">Important Notice</h4>
                          <p className="text-blue-800 text-sm">
                            This policy covers how we handle your data, including interactions with AI models and third-party services.
                            Please read carefully to understand your rights and our practices.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Information We Collect */}
              <section id="information-collection">
                <Card className="border-0 shadow-lg">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center">
                        <Database className="w-5 h-5 text-white" />
                      </div>
                      <CardTitle className="text-xl">Information We Collect</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {/* Data Types Emphasis */}
                      <div className="p-4 bg-blue-50 border border-blue-200 rounded-xl">
                        <div className="flex items-start space-x-3">
                          <Database className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                          <div>
                            <h4 className="font-semibold text-blue-900 mb-2">📊 Types of Data We Collect</h4>
                            <div className="text-blue-800 text-sm space-y-2">
                              <p><strong>Account Information:</strong> Name, email, password, subscription details</p>
                              <p><strong>Usage Data:</strong> AI interactions, prompts, generated content, feature usage</p>
                              <p><strong>Technical Data:</strong> IP address, device info, browser type, session data</p>
                              <p><strong>Communication Data:</strong> Support messages, feedback, survey responses</p>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="prose prose-gray max-w-none">
                        <h4 className="text-lg font-semibold text-gray-900 mb-3">Detailed Information Collection</h4>

                        <div className="grid md:grid-cols-2 gap-6 not-prose">
                          <div className="space-y-4">
                            <h5 className="font-semibold text-gray-900">Personal Information</h5>
                            <ul className="space-y-2 text-gray-700 text-sm">
                              <li>• Full name and email address</li>
                              <li>• Account credentials and preferences</li>
                              <li>• Billing and payment information</li>
                              <li>• Profile settings and customizations</li>
                            </ul>
                          </div>

                          <div className="space-y-4">
                            <h5 className="font-semibold text-gray-900">AI Interaction Data</h5>
                            <ul className="space-y-2 text-gray-700 text-sm">
                              <li>• Prompts and queries submitted</li>
                              <li>• AI model responses and outputs</li>
                              <li>• Conversation history and context</li>
                              <li>• File uploads and attachments</li>
                            </ul>
                          </div>

                          <div className="space-y-4">
                            <h5 className="font-semibold text-gray-900">Technical Information</h5>
                            <ul className="space-y-2 text-gray-700 text-sm">
                              <li>• Device and browser information</li>
                              <li>• IP address and location data</li>
                              <li>• Usage patterns and timestamps</li>
                              <li>• Performance and error logs</li>
                            </ul>
                          </div>

                          <div className="space-y-4">
                            <h5 className="font-semibold text-gray-900">Optional Information</h5>
                            <ul className="space-y-2 text-gray-700 text-sm">
                              <li>• Survey responses and feedback</li>
                              <li>• Marketing preferences</li>
                              <li>• Social media connections</li>
                              <li>• Beta feature participation</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </section>

              {/* How We Use Your Information */}
              <section id="information-use">
                <Card className="border-0 shadow-lg">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                        <Eye className="w-5 h-5 text-white" />
                      </div>
                      <CardTitle className="text-xl">How We Use Your Information</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {/* AI Training Emphasis */}
                      <div className="p-4 bg-purple-50 border border-purple-200 rounded-xl">
                        <div className="flex items-start space-x-3">
                          <Eye className="w-5 h-5 text-purple-600 mt-0.5 flex-shrink-0" />
                          <div>
                            <h4 className="font-semibold text-purple-900 mb-2">🤖 AI Training and Service Improvement</h4>
                            <p className="text-purple-800 text-sm mb-2">
                              <strong>We use your interactions to improve our AI services and user experience.</strong>
                            </p>
                            <ul className="text-purple-800 text-sm space-y-1 list-disc list-inside">
                              <li>Analyzing usage patterns to enhance AI model performance</li>
                              <li>Training and fine-tuning our AI systems (anonymized data only)</li>
                              <li>Improving response quality and accuracy</li>
                              <li>Developing new features and capabilities</li>
                              <li>Optimizing platform performance and reliability</li>
                            </ul>
                          </div>
                        </div>
                      </div>

                      <div className="prose prose-gray max-w-none">
                        <h4 className="text-lg font-semibold text-gray-900 mb-3">Primary Uses of Your Data</h4>
                        <ul className="space-y-2 text-gray-700">
                          <li><strong>Service Provision:</strong> Delivering AI responses, managing your account, processing payments</li>
                          <li><strong>Personalization:</strong> Customizing your experience, remembering preferences, suggesting relevant features</li>
                          <li><strong>Communication:</strong> Sending service updates, support responses, important notifications</li>
                          <li><strong>Security:</strong> Protecting against fraud, abuse, and unauthorized access</li>
                          <li><strong>Analytics:</strong> Understanding usage trends, measuring performance, identifying improvements</li>
                          <li><strong>Legal Compliance:</strong> Meeting regulatory requirements, responding to legal requests</li>
                        </ul>

                        <h4 className="text-lg font-semibold text-gray-900 mb-3 mt-6">Data Processing Legal Basis</h4>
                        <ul className="space-y-2 text-gray-700">
                          <li><strong>Contract Performance:</strong> Processing necessary to provide our services</li>
                          <li><strong>Legitimate Interest:</strong> Improving services, security, and user experience</li>
                          <li><strong>Consent:</strong> Marketing communications and optional features</li>
                          <li><strong>Legal Obligation:</strong> Compliance with applicable laws and regulations</li>
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </section>

              {/* Data Sharing and Disclosure */}
              <section id="data-sharing">
                <Card className="border-0 shadow-lg">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
                        <Users className="w-5 h-5 text-white" />
                      </div>
                      <CardTitle className="text-xl">Data Sharing and Disclosure</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {/* Third-party AI Providers Emphasis */}
                      <div className="p-4 bg-orange-50 border border-orange-200 rounded-xl">
                        <div className="flex items-start space-x-3">
                          <Users className="w-5 h-5 text-orange-600 mt-0.5 flex-shrink-0" />
                          <div>
                            <h4 className="font-semibold text-orange-900 mb-2">🔗 Third-Party AI Provider Data Sharing</h4>
                            <p className="text-orange-800 text-sm mb-2">
                              <strong>We share your prompts and interactions with AI service providers to deliver responses.</strong>
                            </p>
                            <div className="text-orange-800 text-sm space-y-2">
                              <p><strong>AI Providers We Work With:</strong></p>
                              <ul className="list-disc list-inside space-y-1 ml-4">
                                <li>OpenAI (GPT models) - Subject to OpenAI's privacy policy</li>
                                <li>Anthropic (Claude models) - Subject to Anthropic's privacy policy</li>
                                <li>Google (Gemini models) - Subject to Google's privacy policy</li>
                                <li>Other AI providers as we expand our offerings</li>
                              </ul>
                              <p className="mt-2"><strong>Note:</strong> Each provider has their own data handling practices.</p>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="prose prose-gray max-w-none">
                        <h4 className="text-lg font-semibold text-gray-900 mb-3">When We Share Your Data</h4>

                        <div className="grid md:grid-cols-2 gap-6 not-prose">
                          <div className="space-y-4">
                            <h5 className="font-semibold text-gray-900">Service Providers</h5>
                            <ul className="space-y-2 text-gray-700 text-sm">
                              <li>• AI model providers (OpenAI, Anthropic, Google)</li>
                              <li>• Payment processors (Stripe, PayPal)</li>
                              <li>• Cloud hosting services (AWS, Google Cloud)</li>
                              <li>• Analytics providers (Google Analytics)</li>
                              <li>• Customer support tools</li>
                            </ul>
                          </div>

                          <div className="space-y-4">
                            <h5 className="font-semibold text-gray-900">Legal Requirements</h5>
                            <ul className="space-y-2 text-gray-700 text-sm">
                              <li>• Court orders and legal processes</li>
                              <li>• Law enforcement requests</li>
                              <li>• Regulatory compliance requirements</li>
                              <li>• Protection of rights and safety</li>
                              <li>• Fraud prevention and investigation</li>
                            </ul>
                          </div>

                          <div className="space-y-4">
                            <h5 className="font-semibold text-gray-900">Business Transfers</h5>
                            <ul className="space-y-2 text-gray-700 text-sm">
                              <li>• Mergers and acquisitions</li>
                              <li>• Asset sales or transfers</li>
                              <li>• Bankruptcy proceedings</li>
                              <li>• Corporate restructuring</li>
                            </ul>
                          </div>

                          <div className="space-y-4">
                            <h5 className="font-semibold text-gray-900">With Your Consent</h5>
                            <ul className="space-y-2 text-gray-700 text-sm">
                              <li>• Marketing partnerships (opt-in only)</li>
                              <li>• Research collaborations</li>
                              <li>• Beta testing programs</li>
                              <li>• Social media integrations</li>
                            </ul>
                          </div>
                        </div>

                        <h4 className="text-lg font-semibold text-gray-900 mb-3 mt-6">Data Protection Measures</h4>
                        <ul className="space-y-2 text-gray-700">
                          <li>We require all third parties to maintain appropriate security measures</li>
                          <li>Data sharing agreements include privacy and security requirements</li>
                          <li>We limit data sharing to what's necessary for the specific purpose</li>
                          <li>We regularly review and audit our third-party relationships</li>
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </section>

              {/* Data Security and Protection */}
              <section id="data-security">
                <Card className="border-0 shadow-lg">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center">
                        <Lock className="w-5 h-5 text-white" />
                      </div>
                      <CardTitle className="text-xl">Data Security and Protection</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {/* Security Measures Emphasis */}
                      <div className="p-4 bg-green-50 border border-green-200 rounded-xl">
                        <div className="flex items-start space-x-3">
                          <Lock className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
                          <div>
                            <h4 className="font-semibold text-green-900 mb-2">🔒 Encryption and Security Measures</h4>
                            <div className="text-green-800 text-sm space-y-2">
                              <p><strong>Data in Transit:</strong> TLS 1.3 encryption for all communications</p>
                              <p><strong>Data at Rest:</strong> AES-256 encryption for stored data</p>
                              <p><strong>Access Control:</strong> Multi-factor authentication and role-based access</p>
                              <p><strong>Infrastructure:</strong> SOC 2 compliant cloud providers</p>
                              <p><strong>Monitoring:</strong> 24/7 security monitoring and incident response</p>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="prose prose-gray max-w-none">
                        <h4 className="text-lg font-semibold text-gray-900 mb-3">Comprehensive Security Framework</h4>

                        <div className="grid md:grid-cols-2 gap-6 not-prose">
                          <div className="space-y-4">
                            <h5 className="font-semibold text-gray-900">Technical Safeguards</h5>
                            <ul className="space-y-2 text-gray-700 text-sm">
                              <li>• End-to-end encryption protocols</li>
                              <li>• Secure API authentication</li>
                              <li>• Regular security audits and penetration testing</li>
                              <li>• Automated vulnerability scanning</li>
                              <li>• Secure coding practices</li>
                            </ul>
                          </div>

                          <div className="space-y-4">
                            <h5 className="font-semibold text-gray-900">Administrative Controls</h5>
                            <ul className="space-y-2 text-gray-700 text-sm">
                              <li>• Employee background checks</li>
                              <li>• Privacy and security training</li>
                              <li>• Incident response procedures</li>
                              <li>• Data breach notification protocols</li>
                              <li>• Regular policy reviews and updates</li>
                            </ul>
                          </div>

                          <div className="space-y-4">
                            <h5 className="font-semibold text-gray-900">Physical Security</h5>
                            <ul className="space-y-2 text-gray-700 text-sm">
                              <li>• Secure data center facilities</li>
                              <li>• Biometric access controls</li>
                              <li>• 24/7 physical monitoring</li>
                              <li>• Environmental controls and backup power</li>
                              <li>• Secure disposal of hardware</li>
                            </ul>
                          </div>

                          <div className="space-y-4">
                            <h5 className="font-semibold text-gray-900">Compliance Standards</h5>
                            <ul className="space-y-2 text-gray-700 text-sm">
                              <li>• GDPR compliance framework</li>
                              <li>• CCPA privacy requirements</li>
                              <li>• SOC 2 Type II certification</li>
                              <li>• ISO 27001 security standards</li>
                              <li>• Regular compliance audits</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </section>

              {/* Data Retention */}
              <section id="data-retention">
                <Card className="border-0 shadow-lg">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center">
                        <Clock className="w-5 h-5 text-white" />
                      </div>
                      <CardTitle className="text-xl">Data Retention</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      <div className="p-4 bg-blue-50 border border-blue-200 rounded-xl">
                        <div className="flex items-start space-x-3">
                          <Clock className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                          <div>
                            <h4 className="font-semibold text-blue-900 mb-2">⏰ Retention Periods</h4>
                            <div className="text-blue-800 text-sm space-y-2">
                              <p><strong>Account Data:</strong> Retained while account is active + 30 days after deletion</p>
                              <p><strong>Conversation History:</strong> Retained for 90 days unless deleted by user</p>
                              <p><strong>Usage Analytics:</strong> Aggregated data retained for 2 years</p>
                              <p><strong>Billing Records:</strong> Retained for 7 years for tax and legal compliance</p>
                              <p><strong>Support Communications:</strong> Retained for 3 years</p>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="prose prose-gray max-w-none">
                        <h4 className="text-lg font-semibold text-gray-900 mb-3">Retention Principles</h4>
                        <ul className="space-y-2 text-gray-700">
                          <li>We retain data only as long as necessary for legitimate business purposes</li>
                          <li>Data is automatically deleted when retention periods expire</li>
                          <li>You can request early deletion of your personal data</li>
                          <li>Some data may be retained longer for legal compliance</li>
                          <li>Anonymized data may be retained indefinitely for analytics</li>
                        </ul>

                        <h4 className="text-lg font-semibold text-gray-900 mb-3 mt-6">Secure Deletion Process</h4>
                        <ul className="space-y-2 text-gray-700">
                          <li>Data is securely overwritten using industry-standard methods</li>
                          <li>Backup systems are purged according to retention schedules</li>
                          <li>Physical media is destroyed when decommissioned</li>
                          <li>Deletion logs are maintained for audit purposes</li>
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </section>

              {/* Your Privacy Rights */}
              <section id="privacy-rights">
                <Card className="border-0 shadow-lg">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-lg flex items-center justify-center">
                        <UserCheck className="w-5 h-5 text-white" />
                      </div>
                      <CardTitle className="text-xl">Your Privacy Rights</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {/* User Rights Emphasis */}
                      <div className="p-4 bg-purple-50 border border-purple-200 rounded-xl">
                        <div className="flex items-start space-x-3">
                          <UserCheck className="w-5 h-5 text-purple-600 mt-0.5 flex-shrink-0" />
                          <div>
                            <h4 className="font-semibold text-purple-900 mb-2">🛡️ Your Data Rights (GDPR/CCPA Compliance)</h4>
                            <div className="text-purple-800 text-sm space-y-2">
                              <p><strong>Access:</strong> Request a copy of your personal data</p>
                              <p><strong>Rectification:</strong> Correct inaccurate or incomplete data</p>
                              <p><strong>Erasure:</strong> Request deletion of your personal data</p>
                              <p><strong>Portability:</strong> Receive your data in a machine-readable format</p>
                              <p><strong>Restriction:</strong> Limit how we process your data</p>
                              <p><strong>Objection:</strong> Object to processing based on legitimate interests</p>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="prose prose-gray max-w-none">
                        <h4 className="text-lg font-semibold text-gray-900 mb-3">How to Exercise Your Rights</h4>

                        <div className="grid md:grid-cols-2 gap-6 not-prose">
                          <div className="space-y-4">
                            <h5 className="font-semibold text-gray-900">Self-Service Options</h5>
                            <ul className="space-y-2 text-gray-700 text-sm">
                              <li>• Account settings for profile updates</li>
                              <li>• Privacy dashboard for data controls</li>
                              <li>• Conversation deletion tools</li>
                              <li>• Marketing preference center</li>
                              <li>• Data export functionality</li>
                            </ul>
                          </div>

                          <div className="space-y-4">
                            <h5 className="font-semibold text-gray-900">Contact Methods</h5>
                            <ul className="space-y-2 text-gray-700 text-sm">
                              <li>• Email: <EMAIL></li>
                              <li>• Privacy request form on website</li>
                              <li>• In-app privacy settings</li>
                              <li>• Customer support chat</li>
                              <li>• Written request by mail</li>
                            </ul>
                          </div>
                        </div>

                        <h4 className="text-lg font-semibold text-gray-900 mb-3 mt-6">Response Timeframes</h4>
                        <ul className="space-y-2 text-gray-700">
                          <li><strong>GDPR Requests:</strong> Response within 30 days (may extend to 60 days for complex requests)</li>
                          <li><strong>CCPA Requests:</strong> Response within 45 days (may extend to 90 days)</li>
                          <li><strong>Account Deletion:</strong> Processed within 30 days</li>
                          <li><strong>Data Export:</strong> Available within 7 business days</li>
                        </ul>

                        <h4 className="text-lg font-semibold text-gray-900 mb-3 mt-6">Verification Process</h4>
                        <ul className="space-y-2 text-gray-700">
                          <li>We may request additional information to verify your identity</li>
                          <li>Verification helps protect your data from unauthorized access</li>
                          <li>We'll explain what information we need and why</li>
                          <li>Authorized agents may act on your behalf with proper documentation</li>
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </section>

              {/* Cookies and Tracking */}
              <section id="cookies-tracking">
                <Card className="border-0 shadow-lg">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg flex items-center justify-center">
                        <Cookie className="w-5 h-5 text-white" />
                      </div>
                      <CardTitle className="text-xl">Cookies and Tracking</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {/* Cookie Usage Emphasis */}
                      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-xl">
                        <div className="flex items-start space-x-3">
                          <Cookie className="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                          <div>
                            <h4 className="font-semibold text-yellow-900 mb-2">🍪 Cookie Usage and Opt-Out Options</h4>
                            <div className="text-yellow-800 text-sm space-y-2">
                              <p><strong>Essential Cookies:</strong> Required for basic functionality (cannot be disabled)</p>
                              <p><strong>Analytics Cookies:</strong> Help us understand usage patterns (can be disabled)</p>
                              <p><strong>Functional Cookies:</strong> Remember your preferences (can be disabled)</p>
                              <p><strong>Marketing Cookies:</strong> Used for targeted advertising (opt-in only)</p>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="prose prose-gray max-w-none">
                        <h4 className="text-lg font-semibold text-gray-900 mb-3">Types of Cookies We Use</h4>

                        <div className="grid md:grid-cols-2 gap-6 not-prose">
                          <div className="space-y-4">
                            <h5 className="font-semibold text-gray-900">Strictly Necessary</h5>
                            <ul className="space-y-2 text-gray-700 text-sm">
                              <li>• Authentication and session management</li>
                              <li>• Security and fraud prevention</li>
                              <li>• Load balancing and performance</li>
                              <li>• CSRF protection tokens</li>
                            </ul>
                          </div>

                          <div className="space-y-4">
                            <h5 className="font-semibold text-gray-900">Analytics & Performance</h5>
                            <ul className="space-y-2 text-gray-700 text-sm">
                              <li>• Google Analytics (anonymized)</li>
                              <li>• Usage statistics and metrics</li>
                              <li>• Error tracking and debugging</li>
                              <li>• Performance monitoring</li>
                            </ul>
                          </div>

                          <div className="space-y-4">
                            <h5 className="font-semibold text-gray-900">Functional</h5>
                            <ul className="space-y-2 text-gray-700 text-sm">
                              <li>• Language and region preferences</li>
                              <li>• Theme and display settings</li>
                              <li>• Recently used AI models</li>
                              <li>• Accessibility preferences</li>
                            </ul>
                          </div>

                          <div className="space-y-4">
                            <h5 className="font-semibold text-gray-900">Marketing (Optional)</h5>
                            <ul className="space-y-2 text-gray-700 text-sm">
                              <li>• Targeted advertising</li>
                              <li>• Social media integration</li>
                              <li>• Campaign effectiveness tracking</li>
                              <li>• Retargeting pixels</li>
                            </ul>
                          </div>
                        </div>

                        <h4 className="text-lg font-semibold text-gray-900 mb-3 mt-6">Managing Your Cookie Preferences</h4>
                        <ul className="space-y-2 text-gray-700">
                          <li>Use our cookie preference center to control non-essential cookies</li>
                          <li>Configure your browser settings to block or delete cookies</li>
                          <li>Opt out of Google Analytics using their browser add-on</li>
                          <li>Use "Do Not Track" browser settings (we honor these signals)</li>
                          <li>Contact us for assistance with privacy controls</li>
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </section>

              {/* Third-Party Services */}
              <section id="third-party-services">
                <Card className="border-0 shadow-lg">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-teal-500 to-green-500 rounded-lg flex items-center justify-center">
                        <Globe className="w-5 h-5 text-white" />
                      </div>
                      <CardTitle className="text-xl">Third-Party Services</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="prose prose-gray max-w-none">
                      <p className="text-gray-700 mb-4">
                        We integrate with various third-party services to provide our AI platform. Each service has its own privacy policy:
                      </p>

                      <div className="grid md:grid-cols-2 gap-6 not-prose">
                        <div className="space-y-4">
                          <h5 className="font-semibold text-gray-900">AI Providers</h5>
                          <ul className="space-y-2 text-gray-700 text-sm">
                            <li>• OpenAI (GPT models)</li>
                            <li>• Anthropic (Claude models)</li>
                            <li>• Google (Gemini models)</li>
                            <li>• DeepSeek (DeepSeek models)</li>
                            <li>• Other emerging AI providers</li>
                          </ul>
                        </div>

                        <div className="space-y-4">
                          <h5 className="font-semibold text-gray-900">Infrastructure & Analytics</h5>
                          <ul className="space-y-2 text-gray-700 text-sm">
                            <li>• Google Cloud Platform</li>
                            <li>• Amazon Web Services</li>
                            <li>• Google Analytics</li>
                            <li>• Stripe (payments)</li>
                            <li>• SendGrid (email)</li>
                          </ul>
                        </div>
                      </div>

                      <h4 className="text-lg font-semibold text-gray-900 mb-3 mt-6">Data Processing Agreements</h4>
                      <ul className="space-y-2 text-gray-700">
                        <li>We maintain data processing agreements with all third-party providers</li>
                        <li>These agreements ensure appropriate data protection standards</li>
                        <li>We regularly review and update these agreements</li>
                        <li>We only work with providers that meet our security requirements</li>
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              </section>

              {/* International Data Transfers */}
              <section id="international-transfers">
                <Card className="border-0 shadow-lg">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-lg flex items-center justify-center">
                        <Globe className="w-5 h-5 text-white" />
                      </div>
                      <CardTitle className="text-xl">International Data Transfers</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="prose prose-gray max-w-none">
                      <p className="text-gray-700 mb-4">
                        Your data may be transferred to and processed in countries other than your own:
                      </p>

                      <h4 className="text-lg font-semibold text-gray-900 mb-3">Transfer Safeguards</h4>
                      <ul className="space-y-2 text-gray-700">
                        <li>We use Standard Contractual Clauses (SCCs) for EU data transfers</li>
                        <li>We ensure adequate protection levels in destination countries</li>
                        <li>We implement additional safeguards where required</li>
                        <li>We regularly assess the legal landscape in transfer destinations</li>
                      </ul>

                      <h4 className="text-lg font-semibold text-gray-900 mb-3 mt-6">Primary Data Locations</h4>
                      <ul className="space-y-2 text-gray-700">
                        <li><strong>Primary Storage:</strong> United States (Google Cloud, AWS)</li>
                        <li><strong>AI Processing:</strong> United States (OpenAI, Anthropic, Google)</li>
                        <li><strong>Backup Storage:</strong> European Union (for EU users)</li>
                        <li><strong>Analytics:</strong> United States (Google Analytics)</li>
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              </section>

              {/* Children's Privacy */}
              <section id="children-privacy">
                <Card className="border-0 shadow-lg">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-pink-500 to-rose-500 rounded-lg flex items-center justify-center">
                        <Shield className="w-5 h-5 text-white" />
                      </div>
                      <CardTitle className="text-xl">Children's Privacy</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="prose prose-gray max-w-none">
                      <div className="p-4 bg-pink-50 border border-pink-200 rounded-xl mb-4">
                        <p className="text-pink-800 text-sm font-medium">
                          COPPA Compliance: We do not knowingly collect personal information from children under 13.
                        </p>
                      </div>

                      <h4 className="text-lg font-semibold text-gray-900 mb-3">Age Requirements</h4>
                      <ul className="space-y-2 text-gray-700">
                        <li>Users must be at least 13 years old to create an account</li>
                        <li>Users between 13-17 need parental consent in some jurisdictions</li>
                        <li>We verify age during the registration process</li>
                        <li>We may request additional verification if we suspect underage use</li>
                      </ul>

                      <h4 className="text-lg font-semibold text-gray-900 mb-3 mt-6">If We Discover Underage Use</h4>
                      <ul className="space-y-2 text-gray-700">
                        <li>We will immediately delete the account and associated data</li>
                        <li>We will not use or disclose the information for any purpose</li>
                        <li>We will notify parents if required by law</li>
                        <li>We will review our age verification processes</li>
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              </section>

              {/* Changes to Privacy Policy */}
              <section id="policy-changes">
                <Card className="border-0 shadow-lg">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-gray-500 to-slate-500 rounded-lg flex items-center justify-center">
                        <FileText className="w-5 h-5 text-white" />
                      </div>
                      <CardTitle className="text-xl">Changes to Privacy Policy</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="prose prose-gray max-w-none">
                      <h4 className="text-lg font-semibold text-gray-900 mb-3">Update Notification Process</h4>
                      <ul className="space-y-2 text-gray-700">
                        <li>We will notify you of material changes via email</li>
                        <li>We will post updates on our website with effective dates</li>
                        <li>We will provide in-app notifications for significant changes</li>
                        <li>We will maintain a changelog of policy updates</li>
                      </ul>

                      <h4 className="text-lg font-semibold text-gray-900 mb-3 mt-6">Your Options</h4>
                      <ul className="space-y-2 text-gray-700">
                        <li>Review changes before they take effect (30-day notice period)</li>
                        <li>Contact us with questions or concerns about changes</li>
                        <li>Delete your account if you disagree with updates</li>
                        <li>Export your data before policy changes take effect</li>
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              </section>

              {/* Contact Information */}
              <section id="contact">
                <Card className="border-0 shadow-lg">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg flex items-center justify-center">
                        <Mail className="w-5 h-5 text-white" />
                      </div>
                      <CardTitle className="text-xl">Contact Information</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="prose prose-gray max-w-none">
                      <p className="text-gray-700 mb-4">
                        If you have any questions about this Privacy Policy or our data practices, please contact us:
                      </p>

                      <div className="grid md:grid-cols-2 gap-6 not-prose">
                        <div className="space-y-3">
                          <h4 className="text-lg font-semibold text-gray-900">Privacy Inquiries</h4>
                          <div className="space-y-2 text-gray-700">
                            <p className="flex items-center">
                              <Mail className="w-4 h-4 mr-2 text-gray-500" />
                              <EMAIL>
                            </p>
                            <p className="text-sm text-gray-600">
                              For data requests, privacy concerns, and GDPR/CCPA inquiries
                            </p>
                          </div>
                        </div>

                        <div className="space-y-3">
                          <h4 className="text-lg font-semibold text-gray-900">Data Protection Officer</h4>
                          <div className="space-y-2 text-gray-700">
                            <p className="flex items-center">
                              <Mail className="w-4 h-4 mr-2 text-gray-500" />
                              <EMAIL>
                            </p>
                            <p className="text-sm text-gray-600">
                              For complex privacy matters and regulatory compliance
                            </p>
                          </div>
                        </div>
                      </div>

                      <h4 className="text-lg font-semibold text-gray-900 mb-3 mt-6">Response Times</h4>
                      <ul className="space-y-2 text-gray-700">
                        <li><strong>General Privacy Questions:</strong> 2-3 business days</li>
                        <li><strong>Data Subject Requests:</strong> 30 days (GDPR) / 45 days (CCPA)</li>
                        <li><strong>Data Breach Notifications:</strong> Within 72 hours (where required)</li>
                        <li><strong>Urgent Privacy Concerns:</strong> Within 24 hours</li>
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              </section>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="flex items-center space-x-4">
              <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center">
                <Shield className="w-4 h-4 text-white" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900">Promptly</p>
                <p className="text-xs text-gray-500">All your AI, one smart platform</p>
              </div>
            </div>

            <div className="flex items-center space-x-6 text-sm text-gray-600">
              <Link href="/privacy" className="hover:text-gray-900 transition-colors">
                Privacy Policy
              </Link>
              <Link href="/terms" className="hover:text-gray-900 transition-colors">
                Terms of Service
              </Link>
              <Link href="/contact" className="hover:text-gray-900 transition-colors">
                Contact
              </Link>
            </div>

            <div className="text-xs text-gray-500">
              © 2024 Promptly. All rights reserved.
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}