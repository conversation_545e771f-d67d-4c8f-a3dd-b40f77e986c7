// Contact form validation and utilities

export interface ContactFormData {
  name: string
  email: string
  subject: string
  message: string
  phone?: string
}

export interface ContactFormErrors {
  name?: string
  email?: string
  subject?: string
  message?: string
  phone?: string
}

// Validation functions
export const validateName = (name: string): boolean => {
  return name.trim().length >= 2
}

export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export const validateSubject = (subject: string): boolean => {
  return subject.trim().length >= 3
}

export const validateMessage = (message: string): boolean => {
  return message.trim().length >= 10 && message.trim().length <= 2000
}

export const validatePhone = (phone: string): boolean => {
  if (!phone || phone.trim() === '') return true // Optional field
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''))
}

// Comprehensive form validation
export const validateContactForm = (formData: ContactFormData): ContactFormErrors => {
  const errors: ContactFormErrors = {}

  if (!validateName(formData.name)) {
    errors.name = 'Name must be at least 2 characters long'
  }

  if (!validateEmail(formData.email)) {
    errors.email = 'Please enter a valid email address'
  }

  if (!validateSubject(formData.subject)) {
    errors.subject = 'Subject must be at least 3 characters long'
  }

  if (!validateMessage(formData.message)) {
    if (formData.message.trim().length < 10) {
      errors.message = 'Message must be at least 10 characters long'
    } else if (formData.message.trim().length > 2000) {
      errors.message = 'Message must be less than 2000 characters'
    }
  }

  if (formData.phone && !validatePhone(formData.phone)) {
    errors.phone = 'Please enter a valid phone number'
  }

  return errors
}

// Get user agent and basic metadata
export const getContactMetadata = () => {
  if (typeof window === 'undefined') {
    return {
      userAgent: 'Server-side',
      ipAddress: undefined
    }
  }

  return {
    userAgent: navigator.userAgent,
    ipAddress: undefined // IP address would need to be captured server-side
  }
}

// Subject suggestions for better categorization
export const CONTACT_SUBJECTS = [
  'General Inquiry',
  'Technical Support',
  'Billing Question',
  'Feature Request',
  'Bug Report',
  'Account Issue',
  'Partnership Inquiry',
  'Other'
] as const

export type ContactSubject = typeof CONTACT_SUBJECTS[number]

// Character count helper
export const getCharacterCount = (text: string): { count: number; remaining: number; isValid: boolean } => {
  const count = text.length
  const maxLength = 2000
  const remaining = maxLength - count
  const isValid = count >= 10 && count <= maxLength

  return { count, remaining, isValid }
}

// Format phone number for display
export const formatPhoneNumber = (phone: string): string => {
  if (!phone) return ''
  
  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '')
  
  // Format based on length
  if (cleaned.length === 10) {
    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`
  } else if (cleaned.length === 11 && cleaned[0] === '1') {
    return `+1 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`
  }
  
  return phone // Return original if can't format
}

// Sanitize input to prevent XSS
export const sanitizeInput = (input: string): string => {
  return input
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;')
    .trim()
}

// Rate limiting helper (client-side check)
export const checkRateLimit = (): { allowed: boolean; timeUntilReset?: number } => {
  if (typeof window === 'undefined') return { allowed: true }

  const lastSubmission = localStorage.getItem('lastContactSubmission')
  const rateLimitMinutes = 5 // 5 minutes between submissions
  
  if (!lastSubmission) return { allowed: true }

  const lastSubmissionTime = parseInt(lastSubmission)
  const now = Date.now()
  const timeDiff = now - lastSubmissionTime
  const rateLimitMs = rateLimitMinutes * 60 * 1000

  if (timeDiff < rateLimitMs) {
    const timeUntilReset = Math.ceil((rateLimitMs - timeDiff) / 1000 / 60)
    return { allowed: false, timeUntilReset }
  }

  return { allowed: true }
}

// Set rate limit timestamp
export const setRateLimitTimestamp = (): void => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('lastContactSubmission', Date.now().toString())
  }
}
