"use client"

import { useEffect, useRef } from "react"
import { Message } from "@/types/chat"
import { MessageBubble } from "./MessageBubble"
import { StarterQuestions } from "./StarterQuestions"

interface MessageListProps {
  messages: Message[]
  isLoading?: boolean
  onSendMessage?: (message: string) => void
}

export function MessageList({ messages, isLoading = false, onSendMessage }: MessageListProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'end'
      })
    }
  }, [messages, isLoading])

  if (messages.length === 0 && !isLoading) {
    return (
      <StarterQuestions onSendMessage={onSendMessage} />
    )
  }

  return (
    <div
      ref={containerRef}
      className="flex-1 overflow-y-auto scroll-smooth"
    >
      <div className="py-4">
        {messages.map((message) => (
          <MessageBubble
            key={message.id}
            message={message}
          />
        ))}

        {/* Loading indicator */}
        {isLoading && (
          <MessageBubble
            message={{
              id: 'loading',
              content: '',
              role: 'assistant',
              timestamp: new Date()
            }}
            isLoading={true}
          />
        )}

        {/* Scroll anchor */}
        <div ref={messagesEndRef} />
      </div>
    </div>
  )
}
